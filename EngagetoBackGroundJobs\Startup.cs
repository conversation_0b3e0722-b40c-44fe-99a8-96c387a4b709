﻿using Hangfire;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Hangfire.SqlServer;
using Hangfire.Console;
using EngagetoBackGroundJobs.Interfaces;
using EngagetoBackGroundJobs.Implementation;
using Hangfire.Dashboard.BasicAuthorization;

namespace EngagetoBackGroundJobs
{
    public static class Startup
    {
        public static IServiceCollection AddBackgroundJobs(this IServiceCollection services, IConfiguration configuration)
        {
            try
            {
                services.AddScoped<IJobTaskService, JobTaskService>();
                services.AddScoped<IHttpService, HttpService>();
                services.AddScoped<IConversationAnalythicsService, ConversationAnalythicsService>();
                services.AddScoped<ICleanUnusedDataService, CleanUnusedDataService>();
                services.AddScoped<INotificationService, NotificationService>();
                services.AddScoped<ScheduledOperation>();
                services.AddScoped<ICampaignScheduler, CampaignScheduler>();
                services.AddScoped<IContactScheduler, ContactScheduler>();
                services.AddSingleton<LeadProcessingService>();
                services.AddHostedService(provider => provider.GetRequiredService<LeadProcessingService>());
                services.AddHostedService<LeadProcessingService>();
                services.AddHangfire((sp, config) =>
                {
                    config.SetDataCompatibilityLevel(CompatibilityLevel.Version_170)
                          .UseSimpleAssemblyNameTypeSerializer()
                          .UseRecommendedSerializerSettings()
                          .UseSqlServerStorage(configuration.GetConnectionString("ConnStr"), new SqlServerStorageOptions
                          {
                              QueuePollInterval = TimeSpan.FromSeconds(1),
                              SlidingInvisibilityTimeout = TimeSpan.FromMinutes(5),
                              CommandBatchMaxTimeout = TimeSpan.FromMinutes(5),
                              UseRecommendedIsolationLevel = true,
                              DisableGlobalLocks = true
                          })
                          .UseConsole();
                });


                services.AddHangfireServer();
            }
            catch (Exception ex)
            {
                //_logger.Error("Error occurred in AddBackgroundJobs(): " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }));
            }
            return services;
        }
        public static IApplicationBuilder UseHangfireDashboard(this IApplicationBuilder app, IConfiguration config)
        {
            var options = new BackgroundJobServerOptions
            {
                WorkerCount = Environment.ProcessorCount * 5
            };
            app.UseHangfireServer(options);

            app.UseHangfireDashboard("/job-dashboard", new DashboardOptions
            {
                DashboardTitle = "Hangfire Job Engageto",
                DarkModeEnabled = true,
                DisplayStorageConnectionString = false,
                Authorization = new[]
                {
                    new BasicAuthAuthorizationFilter(new BasicAuthAuthorizationFilterOptions
                    {
                        SslRedirect = false,
                        RequireSsl = false,
                        LoginCaseSensitive = true,
                        Users = new []
                        {
                            new BasicAuthAuthorizationUser
                            {
                                Login = "Admin",
                                PasswordClear = "Admin@Password"
                            }
                        }
                    })
                }
            });


            RecurringJob.AddOrUpdate<IJobTaskService>("conversation-analytics-cost-job",
                job => job.GetConversationAnalyticsCost(null),
               "0 1 * * *");
            RecurringJob.AddOrUpdate<IJobTaskService>("clean-un-useddata-job",
                job => job.CleanUnusedDataAsync(null),
                Cron.Daily);

            RecurringJob.AddOrUpdate<IJobTaskService>("send-notification-email-job",
                job => job.SendNotificationAsync(null),
                Cron.Daily);

            //RecurringJob.AddOrUpdate<IJobTaskService>("send-analytics-report-job",
            //    job => job.SendAnalyticsReportNotificationAsync(null),
            //    "0 0 * * 6");
            return app;
        }





    }
}
