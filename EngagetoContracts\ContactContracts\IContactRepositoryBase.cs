﻿using Azure;
using EngagetoEntities.Dtos.CommonDtos;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Dtos;
using System.Data;

namespace EngagetoContracts.ContactContracts
{
    public interface IContactRepositoryBase
    {
        List<Contacts> Get(Guid BusinessId);
        Task AddAll(List<Contacts> Contact);
        Task<bool> DeActivateContactsAsync(Guid[] list);
        void ContactTagsUpdate(Guid[] list, List<Guid> ContactIds);
        void TagsCreate(string Tag, Guid BusinessId, Guid UserId);
        Task<List<Tags>> TagDetails(Guid BusinessId);
        bool TagRemove(Guid TagId, Guid UserId);
        bool TagEdit(TagRequestDto Tag, Guid UserId);
        void NoteAddToContact(List<string> Note, Guid ContactId);
        void NoteRemoveFromContact(List<string> Note, Guid ContactId);
        void ContactEdit(Contacts Contact);
        void ContactTagsRemove(string[] list, List<Guid> ContactIds);
        Task<List<Contacts>> GetContactByContactNumber(string contactNumber, string BusinessId);
        Task<Contacts> SaveContactNumber(string? ContryCode, string contactNo, string BusinessId, EngagetoEntities.Enums.SourceType source, string? name = null, Guid? userId = null);
        Task<bool> RestoreDeletedContactAsync(Guid BusinessId, Guid UserId, Guid[] ContactIds);
        Task<ContactImportTracker> AddAsync(ContactImportTracker entity);
        Task<ContactImportTracker> UpdateAsysnc(ContactImportTracker entity);
        Task<ContactImportTracker> GetByIdAysnc(Guid Id);
        Task<PaginatedResult<ContactResponseDto>> GetAllDeletedContactsAsync(ContactOperationsDto operationsDto, int pageNumber, int pageSize);
        object ContactsDetailsByTag(Guid[] list, Guid BusinessId, Guid userId);
        Task UpdateContactAsync(ContactDetailsDto Contact, Guid contactId, Guid businessId, Guid userId);
        Task<Stream> ContactExportAsync(Guid businessId, Guid userId);
        DataTable ContactExportByIdAsync(ContactsIds contactsIds, Guid businessId, Guid userId);
        Object GetContactDetailsAsync(ContactOperationsDto? Operations, Guid BusinessId, Guid UserId, int page = 1, int per_page = 10);
        Task<(bool IsArchive, string message)> AddContactAsync(Guid businessId, Guid userId, ContactDetailsDto contactInfo);
        Object GetContactDetailsByContactIdAsync(Guid businessId, Guid userId, Guid contactId);
        Task<PaginatedResult<Tags>> GetTagDetailsAsync(Guid businessId, Guid userId, string search, int pageNumber, int pageSize);
        Task<(Contacts, string)> GetContactByContactNumberAndBusinessId(Guid businessId, ContactDetailsDto contactNumber, Guid userId);
        Task<List<Contacts>> GetContactByIds(ContactsIdsDto contactIds);
        Task<(string Message, Contacts Contact)> CreateLeadratContact(LeadratLeadDto leadDto, string businessId);
        Task<List<object>> GetAllSubSourcesBySourceAsync(Guid businessId);
        Task<bool> BlockContactsAsync(string contactNumber);
        Task<bool> UnblockContactAsync(string contactNumber);
        Task<List<object>> GetAllBlockedContactsAsync();
        Task<bool> DeleteContactsAsync(Guid[] contacts, Guid BusinessId);
    }
}
