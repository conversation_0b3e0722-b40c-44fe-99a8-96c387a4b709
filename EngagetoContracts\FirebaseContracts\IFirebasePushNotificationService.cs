using EngagetoEntities.Dtos;
using EngagetoEntities.Entities;

namespace EngagetoContracts.FirebaseContracts
{
    public interface IFirebasePushNotificationService
    {
        Task<FirebaseDeviceRegistrationResponseDto> RegisterDeviceAsync(FirebaseDeviceRegistrationDto deviceDto, Guid userId, Guid businessId);
        Task<bool> RemoveDeviceAsync(string deviceId, Guid userId, Guid businessId);
        Task<FirebaseDeviceRegistrationResponseDto?> UpdateDeviceInfoAsync(string deviceId, FirebaseDeviceInfoUpdateDto updateDto, Guid userId, Guid businessId);
        Task<List<FirebaseDeviceRegistrationResponseDto>> GetUserDevicesAsync(Guid userId, Guid businessId);
        Task<FirebasePushNotificationResponseDto> SendNotificationToUserAsync(Guid userId, Guid businessId, FirebasePushNotificationDto notification);
        Task<FirebasePushNotificationResponseDto> SendNotificationToBusinessUsersAsync(Guid businessId, FirebasePushNotificationDto notification);
        Task<FirebasePushNotificationResponseDto> SendNotificationToTokensAsync(string token, FirebasePushNotificationDto notification);
        Task<bool> ValidateTokenAsync(string fcmToken);
    }
}
