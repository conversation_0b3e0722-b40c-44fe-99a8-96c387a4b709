﻿using EngagetoEntities.Dtos.IntegrationDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using Newtonsoft.Json.Linq;

namespace EngagetoContracts.Services
{
    public interface IIntegrationAccountService : ITransientService
    {
        Task<Guid> AddAccountAsync(BaseIntegrationAccountDto accountDto);
        Task<bool> DeleteAccountAsync(Guid accountId);
        Task<List<ViewIntegrationAccountDto>> GetAccountByIdAsync(Guid accountId);
        Task<List<ViewIntegrationAccountDto>> GetAccountAsync();
        Task<IntegrationWebhookPayloadDto> GetPayloadPropertyMappingAsync(string accountName);
        Task<ViewIntegrationAccountDto> UpdateAccountAsync(EditIntegrationAccountDto account);
        Task<bool> CallExternalApiForLeadAsync(string businessId, JObject obj, ModelMapping modelMapping, IntegrationEvent @event);
        Task SyncLeadInBackgroundAsync(Guid businessId, SourceType source);
        Task<bool> SyncFailedLeadsAsync(Guid businessId, SourceType source);
    }
}
