using EngagetoEntities.Dtos.MasterWorkflowDtos;
using EngagetoEntities.Dtos.WorkflowDtos;

namespace EngagetoContracts.Services
{
    public interface IMasterWorkflowService
    {
        Task<MasterWorkflowResponseDto> CreateMasterWorkflowAsync(CreateMasterWorkflowDto masterWorkflow);
        Task<List<MasterWorkflowResponseDto>> GetAllMasterWorkflowsAsync();
        Task<MasterWorkflowResponseDto> GetMasterWorkflowByIdAsync(Guid id);
        Task<bool> UpdateMasterWorkflowAsync(Guid id, CreateMasterWorkflowDto masterWorkflow);
        Task<bool> DeleteMasterWorkflowAsync(Guid id);
        Task<ViewWorkFlowDto> CreateWorkflowFromMasterAsync(CreateWorkflowFromMasterDto request);
    }
}