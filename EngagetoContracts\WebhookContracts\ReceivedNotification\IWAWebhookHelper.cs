﻿using EngagetoContracts.Services;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;

namespace EngagetoContracts.WebhookContracts.ReceivedNotification
{
    public interface IWAWebhookHelper : ITransientServiceWithScoped
    {
        Task InboxSettingMessageAsync(string businessId, string phoneNumber, string countryCode);
        Task UpdateContactChatStatusAsync(Contacts contact);
        Task ProcessTemplateAsync(EngagetoEntities.Entities.Template template, List<string> values, string companyId, string phoneNumber);
        Task ProcessInteractiveMessageAsync(string businessId, string phoneNumber, string bodyMessage, string  mediaUrl,MediaType mediaType,List<string> values);
    }
}
