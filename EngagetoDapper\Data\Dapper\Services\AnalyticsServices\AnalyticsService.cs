﻿using EngagetoDapper.Data.Dapper.Repositories.InboxRepositories;
using EngagetoDapper.Data.Dtos;
using EngagetoDapper.Data.Interfaces.AnalyticsInterfaces;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserRepositories;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.UserEntities.Dtos;
using Microsoft.EntityFrameworkCore;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using PhoneNumbers;
using System.Drawing;
using System.Linq;

namespace EngagetoDapper.Data.Dapper.Services
{
    public class AnalyticsService : IAnalyticsService
    {
        public readonly IInboxRepository _inboxRepository;
        public readonly IGenericRepository _genericRepository;
        public readonly IUserRepository _userRepository;
        public readonly ApplicationDbContext _contactDb;
        public AnalyticsService(IInboxRepository inboxRepository,
            IGenericRepository genericRepository, ApplicationDbContext contactDb,
            IUserRepository userRepository)
        {
            _inboxRepository = inboxRepository;
            _contactDb = contactDb;
            _genericRepository = genericRepository;
            _userRepository = userRepository;
        }

        public async Task<AnalyticsOverviewDto> GetAnalyticsOverviewAsync(string companyId, AnalyticsOverviewFilterDto overviewFilterDto,
                                                                                           CancellationToken cancellationToken)
        {
            try
            {
                _ = Guid.TryParse(companyId, out Guid businessId);

                var metaAccount = await _genericRepository.GetByObjectAsync<Ahex_CRM_BusinessDetails>(new Dictionary<string, object>()
                {
                    { "Id",companyId },
                    { "Status",true}
                });

                AnalyticsOverviewDto overviewDto = new();
                var contacts = (await _inboxRepository.GetContactsAsync(companyId, cancellationToken))
                               .Where(x => (overviewFilterDto.Tags == null || !overviewFilterDto.Tags.Any()) ||
                                     (x.Tags != null && overviewFilterDto.Tags.Any(tag => x.Tags.Split(',').Contains(tag))) ||
                                     (overviewFilterDto.ContactIds.Any() && overviewFilterDto.ContactIds.Contains(x.ContactId)))
                               .ToList();

                var contactIds = new HashSet<Guid>(contacts.Select(x => x.ContactId));

                var conversations = (await _inboxRepository.GetConversationAsync(companyId, overviewFilterDto.FromDate.Value, overviewFilterDto.ToDate.Value, cancellationToken))
                                                            .Where(x => (x.Status == ConvStatus.delivered || x.Status == ConvStatus.read) &&
                                                                        (x.ContactId != Guid.Empty && contactIds.Contains(x.ContactId ?? Guid.Empty)))
                                                            .ToList();

                //directly distinct contact ids and count them no need to select the contacts and then distinct count
                overviewDto.TotalConversation = conversations
                                                .Where(x => x.ContactId != Guid.Empty)
                                                .Select(x => x.ContactId)
                                                .Distinct()
                                                .Count();

                if (cancellationToken.IsCancellationRequested)
                    cancellationToken.ThrowIfCancellationRequested();

                overviewDto.ConversationDuration = (long)GetAverageConversationDurationTime(conversations);
                var averageResponseTime = CalculateAverageResponseTimeInMinutes(conversations, companyId);
                overviewDto.AverageResponseTime = (long)averageResponseTime;
                overviewDto.ResolutionTimes = CalculateResolutionTime(conversations, contacts, companyId ?? string.Empty);
                overviewDto.AgentResponseTimes = CalculateAverageWaitTimeForAgentResponse(conversations, companyId ?? string.Empty);
                overviewDto.FirstAgentResponseTimes = CalculateWaitTimeForFirstAgentResponse(conversations, companyId ?? string.Empty);
                return overviewDto;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task<List<AgentPerformanceDto>> GetAgentPerformanceAsync(string companyId,
            AnalyticsOverviewFilterDto overviewFilterDto, CancellationToken cancellationToken)
        {
            try
            {
                bool isGuid = Guid.TryParse(companyId, out Guid businessId);
                List<AgentPerformanceDto> performanceDtos = new();
                var contacts = (await _inboxRepository.GetContactsAsync(companyId, cancellationToken))
                   .Where(x => overviewFilterDto.Tags == null ||
                               overviewFilterDto.Tags.Count == 0 ||
                               (x.Tags != null && overviewFilterDto.Tags.Any(tag => x.Tags.Split(',').Contains(tag))));

                var ContactIds = contacts
                                .Where(m => m.ContactId != Guid.Empty)
                                .Select(m => m.ContactId)
                                .ToList();
                var conversations = (await _inboxRepository.GetConversationAsync(companyId, overviewFilterDto.FromDate.Value,  overviewFilterDto.ToDate.Value, cancellationToken))
                                   .Where(x => (x.Status == ConvStatus.delivered || x.Status == ConvStatus.read) && ( x.ContactId != Guid.Empty && ContactIds.Contains(x.ContactId ?? Guid.Empty) ))
                                   .ToList();

                var agents = await _userRepository.GetAgentsAsync<EngagetoEntities.Entities.Ahex_CRM_Users>(companyId);
                //Contact assignment and reassignment histories
                var contactAssignAndReassginHistories = (await _inboxRepository
                    .GetContactAssignmentHistoriesAsync<ContactAssignAndReassignHistoryDto>(businessId,
                            null,
                            overviewFilterDto.FromDate.Value,
                            overviewFilterDto.ToDate.Value.AddDays(1),
                            cancellationToken
                        )).Where(m => contacts.Select(x => x.ContactId).Contains(m.ContactId));


                var AgentAggResponseTimes = CalculateAgentResponseTime(conversations, companyId);
                var respondedCountDetails = (await _inboxRepository.GetRespondedCountAsync(companyId, overviewFilterDto.FromDate.Value, overviewFilterDto.ToDate.Value, cancellationToken))
                    .Where(x => contacts.Select(contact => string.Concat(contact.CountryCode.Replace("+", ""), contact.Contact)).Contains(x.To));

                var allChatStatus = (await _inboxRepository.GetAllChatStatusAsync(companyId, cancellationToken))
                    .Where(x => x.Date.Date >= overviewFilterDto.FromDate.Value.Date && x.Date.Date <= overviewFilterDto.ToDate.Value.Date).
                    Where(m => contacts.Select(x => x.ContactId).Contains(m.ContactId));

                var agentAllChatStatus = (await _inboxRepository.GetAllChatStatusAsync(companyId, cancellationToken, true))
                    .Where(x => x.Date.Date >= overviewFilterDto.FromDate.Value.Date && x.Date.Date <= overviewFilterDto.ToDate.Value.Date).
                    Where(m => contacts.Select(x => x.ContactId).Contains(m.ContactId));

                foreach (var agent in agents)
                {
                    if (cancellationToken.IsCancellationRequested)
                        cancellationToken.ThrowIfCancellationRequested();

                    var agentChatStatus = agentAllChatStatus?
                        .Where(x => x.UserId == agent.Id
                            //&& contacts.Select(contact => contact.ContactId).Contains(x.ContactId)
                            && (x.Status == EngagetoEntities.Enums.ChatStatus.open
                            || x.Status == EngagetoEntities.Enums.ChatStatus.resolved)
                            )
                        // .GroupBy(x => new { x.ContactId, x.UserId })
                        .GroupBy(x => new { x.UserId })
                        .Select(g => new
                        {
                            StartDate = g.Where(c => c.Status == EngagetoEntities.Enums.ChatStatus.open)
                                         .Min(c => (DateTime?)c.Date),
                            EndDate = g.Where(c => c.Status == EngagetoEntities.Enums.ChatStatus.resolved)
                                       .Max(c => (DateTime?)c.Date)
                        })
                        .ToList();

                   // var toContacts = conversations.Where(x => x.UserId?.ToLower() == agent.Id.ToString().ToLower()).Select(x => x.To).Distinct();
                    //var agentMessage = conversations.Where(x => x.UserId?.ToLower() == agent.Id.ToString().ToLower() || toContacts.Contains(x.From));

                    AgentPerformanceDto agentPerformanceDto = new();

                    agentPerformanceDto.Agent = new AgentDto()
                    {
                        Id = agent.Id,
                        Name = agent.Name,
                        RoleId = agent.RoleId,
                        CompanyId = agent.CompanyId
                    };
                    agentPerformanceDto.AssignedCount = contactAssignAndReassginHistories.Where(x => x.UserId == agent.Id)?.Sum(x => x.AssignContactCount) ?? 0;
                    agentPerformanceDto.ReassignedCount = contactAssignAndReassginHistories.Where(x => x.UserId == agent.Id)?.Sum(x => x.ReassignContactCount) ?? 0;

                    agentPerformanceDto.ResolvedCount = allChatStatus?
                        .Where(x => x.UserId == agent.Id && x.Status == EngagetoEntities.Enums.ChatStatus.resolved).Count() ?? 0;

                    agentPerformanceDto.RespondedCount = respondedCountDetails.Count(x => x.UserId?.ToString() == agent.Id.ToString());
                    if (agentChatStatus != null && agentChatStatus.Any())
                    {
                        agentPerformanceDto.ResolutionTime = (int)agentChatStatus
                         .Select(g =>
                         {
                             if (g.StartDate.HasValue && g.EndDate.HasValue)
                             {
                                 if (g.EndDate.Value < g.StartDate.Value)
                                 {
                                     return 0;
                                 }

                                 return (g.EndDate.Value - g.StartDate.Value).TotalMinutes;
                             }
                             else if (g.EndDate.HasValue)
                                 return (g.EndDate.Value - overviewFilterDto.FromDate.Value).TotalMinutes;
                             return 0;
                         })
                         .DefaultIfEmpty(0)
                         .Average();

                    }
                    else
                    {
                        agentPerformanceDto.ResolutionTime = 0;
                    }

                    agentPerformanceDto.AvgResonseTime = AgentAggResponseTimes.FirstOrDefault(x => x.AgentId == agent.Id)?.AvgResponseTime ?? 0;
                    //agentMessage.Any() ? (int)CalculateAgentResponseTime(agentMessage, companyId) : 0;
                    performanceDtos.Add(agentPerformanceDto);
                }
                return performanceDtos;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message, ex);
            }
        }

        public async Task<InboxAnalyticsDasboardDto> GetInboxAnalyticsAsync(string companyId,
            InboxAnalyticsFilterDto inboxAnalyticsFilter, CancellationToken cancellationToken)
        {
            try
            {
                var result = await GetInboxAnalyticsDataAsync(companyId,
                    inboxAnalyticsFilter, cancellationToken);
                return result;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<TenantCCostAnalyticsDto?> GetTenantCCostAnalyticAsync(string tenantId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
        {
            try
            {
                var companyDetails = (await _genericRepository.GetByObjectAsync<Ahex_CRM_BusinessDetails>(new Dictionary<string, object> { { "TenantId", tenantId } }))
                    .FirstOrDefault();
                if (companyDetails != null)
                {
                    var conversationAnalytics = await _inboxRepository.GetCConversationCostAnalyticsAsync(companyDetails.Id.ToString(), fromDate, toDate, cancellationToken) ?? new();
                    conversationAnalytics.TenantId = tenantId;
                    return conversationAnalytics;
                }
                return null;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task<TemplateAnalyticsDto?> GetTemplateAnalyticsAsync(string tenantId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
        {
            try
            {
                var companyDetails = (await _genericRepository.GetByObjectAsync<Ahex_CRM_BusinessDetails>(new Dictionary<string, object> { { "TenantId", tenantId } }))
                    .FirstOrDefault();
                if (companyDetails != null)
                {
                    var conversationAnalytics = await _inboxRepository.GetTemplateAnalyticsAsync(companyDetails.Id.ToString(), fromDate, toDate, cancellationToken) ?? new();
                    conversationAnalytics.TenantId = tenantId;
                    return conversationAnalytics;
                }
                return null;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task<MemoryStream> DownloadReportAsync(string companyId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
        {
            try
            {
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                Guid.TryParse(companyId, out var companyIdGuid);
                var agents = await _userRepository.GetAgentsAsync<EngagetoEntities.Entities.Ahex_CRM_Users>(companyId);
                var allChatStatus = (await _inboxRepository.GetAllChatStatusAsync(companyId, cancellationToken))
                    .Where(x => x.Date.Date >= fromDate.Date && x.Date.Date <= toDate.Date);

                var contactInfos = await _inboxRepository.GetContactInfoAsync(companyIdGuid, fromDate, toDate.AddDays(1), cancellationToken);
                var conversations = await _inboxRepository.GetConversationAsync(companyId, fromDate, toDate, cancellationToken);

                var coustomerMessages = conversations.Where(x => x.From.ToLower() != companyId.ToLower());
                var agentMessages = conversations.Where(x => x.From.ToLower() == companyId.ToLower());
                var coustomerPhoneNumbers = coustomerMessages.Select(x => x.From)
           .Union(agentMessages.Select(x => x.To)).Distinct();


                using (var package = new ExcelPackage())
                {
                    var overviewExcel = package.Workbook.Worksheets.Add("Overview");
                    int rowNo = 2;
                    var columnLength = AnalyticsReportsColumnDto.GetOverViewColumnCount();


                    for (int i = 0; i < columnLength; i++)
                    {
                        var headerCell = overviewExcel.Cells[1, i + 1];
                        headerCell.Value = AnalyticsReportsColumnDto.GetOverViewColumnValue(i);
                        headerCell.Style.Font.Size = 12;
                        headerCell.Style.Font.Color.SetColor(Color.White);
                        headerCell.Style.Fill.PatternType = ExcelFillStyle.Solid;
                        headerCell.Style.Fill.BackgroundColor.SetColor(Color.Black);
                        overviewExcel.Column(i + 1).Width = 20;
                    }
                    foreach (var number in coustomerPhoneNumbers)
                    {
                        var contact = contactInfos.FirstOrDefault(x => x.ContactInfo == number);
                        var CurrentContactData = _contactDb.Contacts.FirstOrDefault
                            (m => (m.CountryCode + m.Contact).Replace("+", "") == number
                            && m.BusinessId.ToString().ToLower() == companyId.ToLower() && m.IsActive && !m.IsDeleted);
                        if (contact == null)
                            continue;

                        var firstConversation = conversations
                            .Where(x => x.From == number || x.To == number)
                            .OrderBy(x => x.CreatedAt).FirstOrDefault();

                        var firstCustomerMessage = coustomerMessages
                            .Where(x => x.From == number && x.To.ToLower() == companyId.ToLower())
                            .OrderBy(x => x.CreatedAt).FirstOrDefault();


                        var firstagentResponse = agentMessages
                            .Where(x => x.From.ToLower() == companyId.ToLower() && x.To == number && x.CreatedAt > firstCustomerMessage?.CreatedAt && x.UserId != null)
                            .OrderBy(x => x.CreatedAt).FirstOrDefault();

                        var allConversations = conversations.Where(x => x.From == number || x.To == number);

                        var openSatatus = allChatStatus
                            .Where(x => x.ContactId == contact.ContactId && x.Status == EngagetoEntities.Enums.ChatStatus.open)
                            .OrderByDescending(x => x.Date < contact.Date).FirstOrDefault();

                        overviewExcel.Cells[rowNo, 1].Value = contact.CountryCode;
                        overviewExcel.Cells[rowNo, 2].Value = contact.Contact;
                        overviewExcel.Cells[rowNo, 3].Value = firstConversation?.CreatedAt.ToString() ?? string.Empty;
                        overviewExcel.Cells[rowNo, 4].Value = firstConversation?.From.ToLower() == companyId.ToLower() ? "Agent" : "Customer";
                        overviewExcel.Cells[rowNo, 5].Value = agentMessages
                            .Where(x => x.From.ToLower() == companyId.ToLower() && x.To == number)
                            .OrderBy(x => x.CreatedAt).FirstOrDefault()?.CreatedAt.ToString();
                        overviewExcel.Cells[rowNo, 6].Value = coustomerMessages
                            .Where(x => x.From == number && x.To.ToLower() == companyId.ToLower())?.Count() ?? 0;

                        overviewExcel.Cells[rowNo, 7].Value = agentMessages
                            .Where(x => x.From.ToLower() == companyId.ToLower() && x.To == number)?.Count() ?? 0;

                        overviewExcel.Cells[rowNo, 8].Value = contact.Status == EngagetoEntities.Enums.ChatStatus.resolved ? contact.Date.ToString() : string.Empty;
                        overviewExcel.Cells[rowNo, 9].Value = (int?)(firstagentResponse?.CreatedAt - firstCustomerMessage?.CreatedAt)?.TotalMinutes;
                        overviewExcel.Cells[rowNo, 10].Value = allConversations.Any() ? (int?)CalculateAverageResponseTimeInMinutes(allConversations, companyId) : 0;

                        overviewExcel.Cells[rowNo, 11].Value = CurrentContactData.ChatStatus == EngagetoEntities.Enums.ChatStatus.resolved
                        ? Math.Max((int?)(contact.Date - (openSatatus?.Date ?? fromDate))?.TotalMinutes ?? 0, 0) : 0;



                        overviewExcel.Cells[rowNo, 12].Value = CurrentContactData.ChatStatus == EngagetoEntities.Enums.ChatStatus.resolved
                            ? agents.FirstOrDefault(x => x.Id == CurrentContactData.UserId)?.EmailAddress : string.Empty;

                        // overviewExcel.Cells[rowNo, 13].Value = string.Empty;
                        //if(contact.Contact == "9373202585")
                        //    await Console.Out.WriteLineAsync(contact.Status.ToString());

                        overviewExcel.Cells[rowNo, 13].Value = CurrentContactData?.ChatStatus.ToString() ?? string.Empty;

                        rowNo++;
                    }
                    var Value = overviewExcel.Cells.Value;
                    var memoryStream = new MemoryStream();
                    package.SaveAs(memoryStream);
                    memoryStream.Position = 0;
                    return memoryStream;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"An error occurred while generating the report: {ex.Message}", ex);
            }
        }

        /* public async Task<MemoryStream> DownalodAgentPerformanceReportAsync(string companyId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
         {
             ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
             Guid.TryParse(companyId, out var companyIdGuid);
             var agents = await _userRepository.GetAgentsAsync<Ahex_CRM_Users>(companyId, null);
             var allChatStatus = await _inboxRepository.GetAllChatStatusAsync(companyId, cancellationToken);
             var contactInfos = await _inboxRepository.GetContactInfoAsync(companyIdGuid, fromDate.AddDays(-1), toDate.AddDays(1), cancellationToken);
             var conversations = await _inboxRepository.GetConversationAsync(companyId, fromDate, toDate, cancellationToken);
             //Assign and Reassign Contacts history
             var assignAndReassignContacts = await _genericRepository.GetByObjectAsync<ContactAssignmentHistoryEntity>(new Dictionary<string, object>
             {
                 { "CompanyId",companyIdGuid}
             });
             var coustomerMessages = conversations.Where(x => x.From.ToLower() != companyId.ToLower());
             var agentMessages = conversations.Where(x => x.From.ToLower() == companyId.ToLower());
             var coustomerPhoneNumbers = coustomerMessages.Select(x => x.From).Distinct();

             using (var package = new ExcelPackage())
             {
                 var overviewExcel = package.Workbook.Worksheets.Add("Overview");
                 int rowNo = 2;
                 var columnLength = AnalyticsReportsColumnDto.GetAgentPerformanceColumnCount();

                 for (int i = 0; i < columnLength; i++)
                 {
                     var headerCell = overviewExcel.Cells[1, i + 1];
                     headerCell.Value = AnalyticsReportsColumnDto.GetAgentPerformanceColumnValue(i);
                     headerCell.Style.Font.Size = 12;
                     headerCell.Style.Font.Color.SetColor(Color.White);
                     headerCell.Style.Fill.PatternType = ExcelFillStyle.Solid;
                     headerCell.Style.Fill.BackgroundColor.SetColor(Color.Black);
                     overviewExcel.Column(i + 1).Width = 20;
                 }
                 foreach (var number in coustomerPhoneNumbers)
                 {
                     var contact = contactInfos.FirstOrDefault(x => x.ContactInfo == number);
                     if (contact == null)
                         continue;

                     var assignmentContacts = assignAndReassignContacts
                         .Where(x => x.ContactId == contact.ContactId)
                         .OrderBy(x => x.AssignDate);

                     var firstConversation = conversations
                         .Where(x => x.From == number || x.To == number)
                         .OrderBy(x => x.CreatedAt).FirstOrDefault();

                     var firstCustomerMessage = coustomerMessages
                         .Where(x => x.From == number && x.To.ToLower() == companyId.ToLower())
                         .OrderBy(x => x.CreatedAt).FirstOrDefault();

                     var firstagentResponse = agentMessages
                         .Where(x => x.From.ToLower() == companyId.ToLower() && x.To == number && x.CreatedAt > firstCustomerMessage?.CreatedAt)
                         .OrderBy(x => x.CreatedAt).FirstOrDefault();

                     var allConversations = conversations.Where(x => x.From == number || x.To == number);

                     var openSatatus = allChatStatus
                         .Where(x => x.ContactId == contact.ContactId && x.Status == EngagetoEntities.ContactEntities.Models.ChatStatus.open)
                         .OrderByDescending(x => x.Date < contact.Date).FirstOrDefault();

                     overviewExcel.Cells[rowNo, 1].Value = contact.CountryCode;
                     overviewExcel.Cells[rowNo, 2].Value = contact.Contact;
                     overviewExcel.Cells[rowNo, 3].Value = agents.FirstOrDefault(x => x.Id == assignmentContacts.FirstOrDefault()?.AssignedToUserId)?.Name ?? string.Empty;
                     //contact.UserId != null ? agents.FirstOrDefault(x => x.Id == contact.UserId)?.Name : string.Empty;
                     overviewExcel.Cells[rowNo, 4].Value = firstCustomerMessage?.CreatedAt.ToString();
                     overviewExcel.Cells[rowNo, 5].Value = assignmentContacts.FirstOrDefault()?.AssignDate.ToString();
                     overviewExcel.Cells[rowNo, 6].Value = firstagentResponse?.CreatedAt.ToString();
                     overviewExcel.Cells[rowNo, 7].Value = assignmentContacts.LastOrDefault()?.AssignDate.ToString();
                     overviewExcel.Cells[rowNo, 8].Value = contact.Status == EngagetoEntities.ContactEntities.Models.ChatStatus.resolved ? contact.Date.ToString() : string.Empty;

                     overviewExcel.Cells[rowNo, 9].Value = allConversations.Count(x => x.From.ToLower() != companyId.ToLower());

                     overviewExcel.Cells[rowNo, 10].Value = allConversations.Count(x => x.From.ToLower() == companyId.ToLower());

                     overviewExcel.Cells[rowNo, 11].Value = (int?)(firstagentResponse?.CreatedAt - firstCustomerMessage?.CreatedAt)?.TotalMinutes;

                     overviewExcel.Cells[rowNo, 12].Value = allConversations.Any() ? (int?)CalculateAverageResponseTimeInMinutes(allConversations, companyId) : 0;

                     overviewExcel.Cells[rowNo, 13].Value = contact.Status == EngagetoEntities.ContactEntities.Models.ChatStatus.resolved
                         ? (int?)(contact.Date - openSatatus?.Date)?.TotalMinutes : 0;
                     rowNo++;
                 }

                 var memoryStream = new MemoryStream();
                 package.SaveAs(memoryStream);
                 memoryStream.Position = 0;
                 return memoryStream;
             }
         }
 */
        public async Task<MemoryStream> DownalodAgentPerformanceReportAsync(string companyId, AnalyticsOverviewFilterDto filterDot, CancellationToken cancellationToken)
        {
            try
            {
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                var data = await GetAgentPerformanceAsync(companyId, filterDot, cancellationToken);
                using (var package = new ExcelPackage())
                {
                    // Add a new worksheet for Agent Performance
                    ExcelWorksheet worksheet = package.Workbook.Worksheets.Add("Agent Performance");
                    var columnNames = AnalyticsReportsColumnDto.GetAgentPerformanceColumnCount();
                    // Add column headers with formatting
                    for (int i = 0; i < columnNames; i++)
                    {
                        var headerCell = worksheet.Cells[1, i + 1];
                        headerCell.Value = AnalyticsReportsColumnDto.GetAgentPerformanceColumnValue(i);
                        headerCell.Style.Font.Size = 12;
                        headerCell.Style.Font.Color.SetColor(System.Drawing.Color.White);
                        headerCell.Style.Fill.PatternType = ExcelFillStyle.Solid;
                        headerCell.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.Black);
                        worksheet.Column(i + 1).Width = 20;
                    }

                    // Add data to worksheet
                    int rowNo = 2; // Start from the second row
                    foreach (var agentPerformance in data)
                    {
                        worksheet.Cells[rowNo, 1].Value = agentPerformance.Agent?.Name ?? string.Empty;
                        worksheet.Cells[rowNo, 2].Value = agentPerformance.AssignedCount;
                        worksheet.Cells[rowNo, 3].Value = agentPerformance.ReassignedCount;
                        worksheet.Cells[rowNo, 4].Value = agentPerformance.RespondedCount;
                        worksheet.Cells[rowNo, 5].Value = agentPerformance.ResolvedCount.HasValue ? agentPerformance.ResolvedCount.Value : 0;
                        worksheet.Cells[rowNo, 6].Value = agentPerformance.ResolutionTime;
                        worksheet.Cells[rowNo, 7].Value = agentPerformance.AvgResonseTime;

                        rowNo++;
                    }

                    // Save the package into memory stream
                    var memoryStream = new MemoryStream();
                    package.SaveAs(memoryStream);
                    memoryStream.Position = 0;
                    return memoryStream;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"An error occurred while generating the report: {ex.Message}", ex);
            }
        }

        public async Task<MemoryStream> DownloadInboxAnalyticsReportAsync(string companyId,
            InboxAnalyticsFilterDto inboxAnalyticsFilter, CancellationToken cancellationToken)
        {
            try
            {
                var result = await GetInboxAnalyticsDataAsync(companyId,
                   inboxAnalyticsFilter, cancellationToken);

                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                var analyticsFormatsData = AnalyticsReportsColumnDto.GetInboxAnalyticsColumns();
                using (var package = new ExcelPackage())
                {
                    foreach (var format in analyticsFormatsData)
                    {
                        var overviewExcel = package.Workbook.Worksheets.Add(format.Key);
                        int rowNo = 2;
                        var columnLength = format.Value.Length;

                        for (int i = 0; i < columnLength; i++)
                        {
                            var headerCell = overviewExcel.Cells[1, i + 1];
                            headerCell.Value = format.Value[i];
                            headerCell.Style.Font.Size = 12;
                            headerCell.Style.Font.Color.SetColor(Color.White);
                            headerCell.Style.Fill.PatternType = ExcelFillStyle.Solid;
                            headerCell.Style.Fill.BackgroundColor.SetColor(Color.Black);
                            overviewExcel.Column(i + 1).Width = 20;
                        }
                        if (format.Key == "Chat")
                        {
                            overviewExcel.Cells[rowNo, 1].Value = result.InboxAnalytics.OpenCount;
                            overviewExcel.Cells[rowNo, 2].Value = result.InboxAnalytics.PendingCount;
                            overviewExcel.Cells[rowNo, 3].Value = result.InboxAnalytics.ResolvedCount;
                            overviewExcel.Cells[rowNo, 4].Value = result.InboxAnalytics.ExpiredCount;
                            // overviewExcel.Cells[rowNo, 5].Value = result.InboxAnalytics.UnAssinedCount;

                        }
                        else if (format.Key == "Ticket Total")
                        {
                            overviewExcel.Cells[2, 1].Value = "Sent";
                            overviewExcel.Cells[2, 2].Value = result.AnalyticsTotalMessage.SentCount;

                            overviewExcel.Cells[3, 1].Value = "Received";
                            overviewExcel.Cells[3, 2].Value = result.AnalyticsTotalMessage.ReceivedCount;

                            overviewExcel.Cells[4, 1].Value = "Faild";
                            overviewExcel.Cells[4, 2].Value = result.AnalyticsTotalMessage.FaildCount;

                            overviewExcel.Cells[5, 1].Value = "Spam";
                            overviewExcel.Cells[5, 2].Value = result.AnalyticsTotalMessage.SpamCount;

                        }
                        else if (format.Key == "Top Users")
                        {
                            var userPerformanceDto = result.TopUserPerformance.OrderByDescending(x => x.MessageCount).ToList();
                            foreach (var user in userPerformanceDto)
                            {
                                overviewExcel.Cells[rowNo, 1].Value = user.Name;
                                overviewExcel.Cells[rowNo, 2].Value = user.MessageCount;
                                rowNo++;
                            }
                        }
                        else if (format.Key == "Ticket Status")
                        {
                            foreach (var statusChart in result.StatusChat)
                            {
                                var splitDate = statusChart.MonthYear.Split("-");
                                int.TryParse(splitDate[0], out int year);
                                int.TryParse(splitDate[1], out int month);
                                overviewExcel.Cells[rowNo, 1].Value = new DateTime(year, month, 1).ToString("MMM");
                                overviewExcel.Cells[rowNo, 2].Value = statusChart.OpenCount;
                                overviewExcel.Cells[rowNo, 3].Value = statusChart.PendingCount;
                                overviewExcel.Cells[rowNo, 4].Value = statusChart.ResolvedCount;
                                overviewExcel.Cells[rowNo, 5].Value = statusChart.ExpiredCount;
                                rowNo++;
                            }
                        }
                    }
                    var memoryStream = new MemoryStream();
                    package.SaveAs(memoryStream);
                    memoryStream.Position = 0;
                    return memoryStream;
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        #region Helper method
        #region Inbox Analytics Methods
        public List<StatusChatDto> GetTicketStatusData(IEnumerable<ChatStatusEntityDto> chatStatuses, DateTime startDate, DateTime endDate,
            List<Contacts> contacts, List<Conversations> conversations)
        {
            // Generate all months between startDate and endDate
            var allMonths = Enumerable.Range(0, ((endDate.Year - startDate.Year) * 12) + endDate.Month - startDate.Month + 1)
                .Select(offset => startDate.AddMonths(offset).ToString("yyyy-MM"))
                .ToList();

            // Group conversations by month-year and chat ID
            var groupedConversations = conversations
                .GroupBy(c => c.CreatedAt.ToString("yyyy-MM"))
                .ToDictionary(g => g.Key, g => g.ToList());

            // Calculate pending count per month
            var pendingCountByMonth = allMonths.ToDictionary(month => month, month => contacts.Count(contact =>
            {
                var msg = groupedConversations.ContainsKey(month)
                    ? groupedConversations[month]
                        .Where(m => ( m.ContactId == contact.ContactId) &&
                                    (m.MessageType == MessageType.Normal ||
                                     m.MessageType == MessageType.AutoReply))
                        .OrderByDescending(m => m.CreatedAt)
                        .FirstOrDefault()
                    : null;

                return msg != null && msg.From?.ToLower() != contact.BusinessId.ToString().ToLower();
            }));

            // Group chat statuses by month-year and generate status counts
            var groupedData = chatStatuses
                .GroupBy(cs => cs.Date.ToString("yyyy-MM"))
                .ToDictionary(g => g.Key, g => new StatusChatDto
                {
                    MonthYear = g.Key,
                    OpenCount = g.Count(cs => cs.Status == EngagetoEntities.Enums.ChatStatus.open),
                    PendingCount = pendingCountByMonth[g.Key],
                    ResolvedCount = g.Count(cs => cs.Status == EngagetoEntities.Enums.ChatStatus.resolved),
                    ExpiredCount = g.Count(cs => cs.Status == EngagetoEntities.Enums.ChatStatus.expired)
                });

            // Return ticket status data with all months
            return allMonths.Select(month => groupedData.ContainsKey(month) ? groupedData[month] : new StatusChatDto
            {
                MonthYear = month,
                OpenCount = 0,
                PendingCount = pendingCountByMonth[month],
                ResolvedCount = 0,
                ExpiredCount = 0
            })
            .OrderBy(dto => dto.MonthYear)
            .ToList();
        }


        public async Task<InboxAnalyticsDasboardDto> GetInboxAnalyticsDataAsync(string companyId,
            InboxAnalyticsFilterDto inboxAnalyticsFilter, CancellationToken cancellationToken)
        {
            try
            {
                if (cancellationToken.IsCancellationRequested)
                    cancellationToken.ThrowIfCancellationRequested();

                var contacts = (await _inboxRepository.GetContactsAsync(companyId, cancellationToken))
                   .Where(x => inboxAnalyticsFilter.Tags == null ||
                               inboxAnalyticsFilter.Tags.Count == 0 ||
                               (x.Tags != null && inboxAnalyticsFilter.Tags.Any(tag => x.Tags.Split(',').Contains(tag))))
                   .ToList();

                var allChatStatus = (await _inboxRepository.GetAllChatStatusAsync(companyId, cancellationToken))
                        .Where(chat => contacts.Select(contact => contact.ContactId).Contains(chat.ContactId))
                        .GroupBy(chat => chat.ContactId)
                        .Select(group => group.OrderByDescending(chat => chat.Date).First());
                int pendingCount = 0;
                DateTime fromDate = DateTime.MinValue;
                DateTime toDate = DateTime.MaxValue;

                // Apply Date Filter
                if (inboxAnalyticsFilter.FromDate != null)
                {
                    fromDate = inboxAnalyticsFilter.FromDate.Value.Date;
                    toDate = inboxAnalyticsFilter.ToDate?.Date.AddDays(1).AddTicks(-1) ?? fromDate.AddDays(1).AddTicks(-1);
                    allChatStatus = allChatStatus.Where(x => x.Date >= fromDate && x.Date <= toDate);
                }

                var conversations = await _inboxRepository.GetConversationAsync(companyId, fromDate, toDate, cancellationToken);

                foreach (var contact in contacts)
                {
                    if (contact != null)
                    {                    
                        var msg = conversations
                            .Where(m => (m.ContactId == contact.ContactId) &&
                            (m.MessageType == MessageType.Normal ||
                            m.MessageType == MessageType.AutoReply))
                            .OrderByDescending(m => m.CreatedAt)
                            .FirstOrDefault();

                        if (msg != null && msg.From?.ToLower() != contact.BusinessId.ToString().ToLower())
                        {
                            pendingCount++;
                        }
                    }
                };

                /* if (inboxAnalyticsFilter.FromDate != null)

                     allChatStatus = allChatStatus.Where(x => x.Date.Date >= inboxAnalyticsFilter.FromDate?.Date
                         && x.Date.Date <= inboxAnalyticsFilter.ToDate?.Date);*/
                //inbox analytics dasboard report
                InboxAnalyticsDto inboxAnalyticsDto = new()
                {
                    OpenCount = allChatStatus
                        .Where(x => x.Status == EngagetoEntities.Enums.ChatStatus.open)
                        .Count(),
                    PendingCount = pendingCount,
                    ResolvedCount = allChatStatus
                        .Where(x => x.Status == EngagetoEntities.Enums.ChatStatus.resolved)
                        .Count(),
                    ExpiredCount = allChatStatus
                        .Where(x => x.Status == EngagetoEntities.Enums.ChatStatus.expired)
                        .Count(),
                    UnAssinedCount = contacts.Where(x => x.UserId == null).Count()
                };
                // ticket status chatbar report
                var statusChatDto = GetTicketStatusData(allChatStatus, inboxAnalyticsFilter.FromDate.Value.Date,
                    inboxAnalyticsFilter.ToDate.Value.Date, contacts, conversations.ToList());

                //total message report
                InboxAnalyticsTotalMessagsDto inboxAnalyticsTotalMessags = (await _inboxRepository
                    .GetInboxTotalMessageCountAsync(contacts, companyId,
                        inboxAnalyticsFilter.FromDate,
                        inboxAnalyticsFilter.ToDate,
                        cancellationToken)).FirstOrDefault()
                        ?? new();
                inboxAnalyticsTotalMessags.SpamCount = contacts.Count(x => x.IsSpam ?? false);
                //top user performance 
                List<TopUserPerformanceDto> userPerformanceDto = (await _inboxRepository
                    .GetUserPerformanceAsync(contacts,
                    companyId, inboxAnalyticsFilter.FromDate,
                        inboxAnalyticsFilter.ToDate,
                        cancellationToken))?.ToList()
                         ?? new List<TopUserPerformanceDto>();


                return new InboxAnalyticsDasboardDto()
                {
                    InboxAnalytics = inboxAnalyticsDto,
                    StatusChat = statusChatDto,
                    AnalyticsTotalMessage = inboxAnalyticsTotalMessags,
                    TopUserPerformance = userPerformanceDto.OrderByDescending(m => m.MessageCount).ToList()
                };

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        #endregion

        #region Overview Analytics Methods
        private double GetAverageConversationDurationTime(IEnumerable<Conversations> conversations)
        {
            var conversationDurations = conversations
                                            .GroupBy(c => new
                                            {
                                                From = c.From.ToLower(),
                                                To = c.To.ToLower()
                                            })

                                            .Select(g => new
                                            {
                                                ConversationStart = g.Min(c => c.CreatedAt),
                                                ConversationEnd = g.Max(c => c.CreatedAt)
                                            })
                                            .ToList();

            if (!conversationDurations.Any())
                return 0;

            var averageConversationDurationTime = conversationDurations
                .Average(cd => (cd.ConversationEnd - cd.ConversationStart).TotalMinutes);

            return averageConversationDurationTime;
        }

        private double CalculateAverageResponseTimeInMinutes(IEnumerable<Conversations> conversations, string companyId)
        {
            // Agent Messages Query
            var agentMessagesQuery = conversations
                .Where(cs => cs.From.ToLowerInvariant() == companyId.ToLowerInvariant() && cs.UserId != null)
                .Select(cs => new
                {
                    cs.Id,
                    cs.From,
                    cs.To,
                    cs.CreatedAt,
                    cs.UserId
                })
                .OrderBy(cs => cs.CreatedAt)
                .ToList();

            // Customer Messages Query
            var customerMessagesQuery = conversations
            .Where(cs => cs.From != companyId && cs.To.ToLowerInvariant() == companyId.ToLowerInvariant())
            .Select(cs => new
            {
                cs.Id,
                cs.From,
                cs.To,
                cs.CreatedAt
            })
                .OrderBy(cs => cs.CreatedAt)
                .ToList();

            var responseTimesList = new List<dynamic>();

            var joinedMessages = from cm in customerMessagesQuery
                                 join am in agentMessagesQuery
                                     on new { From = cm.To.ToLowerInvariant(), To = cm.From.ToLowerInvariant() } equals new { am.From, am.To }
                                 where am.CreatedAt > cm.CreatedAt
                                 select new
                                 {
                                     CustomerMessage = cm,
                                     AgentMessage = am
                                 };

            var responsePairs = joinedMessages
                .GroupBy(jm => jm.CustomerMessage.Id)
                .Select(g => g.OrderBy(jm => jm.AgentMessage.CreatedAt).FirstOrDefault())
                .ToList();

            foreach (var pair in responsePairs)
            {
                if (pair != null)
                {
                    responseTimesList.Add(new
                    {
                        CustomerPhoneNumber = pair.CustomerMessage.From,

                        AgentId = pair.AgentMessage.From,
                        ResponseTime = CalculateDateDiffInMinutes(pair.CustomerMessage.CreatedAt, pair.AgentMessage.CreatedAt),
                        CCreatedAt = pair.CustomerMessage.CreatedAt,
                        ACreatedAt = pair.AgentMessage.CreatedAt
                    });
                }
            }
            responseTimesList = responseTimesList.OrderByDescending(x => x.CCreatedAt)?.ToList() ?? new List<dynamic>();
            var uniqueAgentResponses = new HashSet<(string CustomerPhoneNumber, string AgentId, DateTime ACreatedAt)>();
            var uniqueCustomerResponses = new HashSet<(string CustomerPhoneNumber, string AgentId, DateTime CCreatedAt)>();
            // Step 3: Use HashSet for Efficient Filtering
            var filteredResponses = new List<dynamic>();
            foreach (var message in responseTimesList)
            {
                var key = ((string)message.CustomerPhoneNumber, (string)message.AgentId, (DateTime)message.ACreatedAt);
                var customerKey = ((string)message.CustomerPhoneNumber, (string)message.AgentId, (DateTime)message.CCreatedAt);
                if (uniqueAgentResponses.Add(key) && uniqueCustomerResponses.Add(customerKey))
                {
                    filteredResponses.Add(message);
                }
            }
            var chatAvgResponseTimes = filteredResponses
                .GroupBy(rm => new { rm.AgentId, rm.CustomerPhoneNumber })
                .Select(grp => new
                {
                    AgentId = grp.Key.AgentId,
                    AvgResponseTimePerChat = grp.Average(rm => rm.ResponseTime)
                })
                .ToList();

            var overallAvgResponseTimes = chatAvgResponseTimes
                .GroupBy(car => car.AgentId)
                .Select(grp => new AgentAvgResponseTimeDto
                {
                    AgentId = Guid.Parse(grp.Key),
                    AvgResponseTime = (long)grp.Average(car => car.AvgResponseTimePerChat)
                })
                .ToList();

            return overallAvgResponseTimes.Any() ? overallAvgResponseTimes.Average(x => x.AvgResponseTime) : 0;
        }

        private List<WaitTimeDataDto> CalculateResolutionTime(IEnumerable<Conversations> conversations, IEnumerable<Contacts> contacts, string companyId)
        {
            var result = new List<WaitTimeDataDto>();

            var uniqueToContactNumbers = new HashSet<string>(
                conversations.Where(x => x.To.ToLower() != companyId)
                             .Select(x => x.To.ToLower())
            );
            var resolvedContacts = new HashSet<string>(
                contacts.Where(x => x.ChatStatus == EngagetoEntities.Enums.ChatStatus.resolved && uniqueToContactNumbers.Contains(x.CountryCode.Replace("+", "") + x.Contact))
                        .Select(x => string.Concat(x.CountryCode.Replace("+", ""), x.Contact))
            );

            var conversationDurations = conversations
                .Where(x => resolvedContacts.Contains(x.To))
                .GroupBy(c => new { c.From, c.To })
                .Select(g => (g.Max(c => c.CreatedAt) - g.Min(c => c.CreatedAt)).TotalMinutes )
                .ToList();

            var timeRanges = new Dictionary<string, Func<double, bool>>
                {
                    { "<10m", rt => rt < 10 },
                    { "10-50m", rt => rt >= 10 && rt < 50 },
                    { "50-75m", rt => rt >= 50 && rt < 75 },
                    { "75-100m", rt => rt >= 75 && rt < 100 },
                    { ">100m", rt => rt >= 100 }
                };

            result.AddRange(timeRanges.Select(range => new WaitTimeDataDto
            {
                Range = range.Key,
                Count = conversationDurations.Count(range.Value)
            }));

            return result;
        }
        public List<WaitTimeDataDto> CalculateAverageWaitTimeForAgentResponse(IEnumerable<Conversations> conversations, string agentPhoneNumber)
        {
            var responseTimes = new List<double>();
            var result = new List<WaitTimeDataDto>();

            // Group conversations by customer
            var groupedConversations = conversations
                .Where(c => c.From.ToLower() == agentPhoneNumber.ToLower())
                .GroupBy(c => c.To.ToLower());

            foreach (var customerGroup in groupedConversations)
            {
                var customerMessages = conversations
                    .Where(x => x.From.ToLower() != agentPhoneNumber.ToLower()
                        && x.To.ToLower() == agentPhoneNumber.ToLower()
                        && x.From.ToLower() == customerGroup.Key)
                    .OrderBy(c => c.CreatedAt)
                    .ToList();

                double totalWaitTime = 0;
                int countOfCustomerMessages = 0;

                foreach (var customerMessage in customerMessages)
                {
                    // Find the next agent response after the customer message
                    var agentResponse = customerGroup
                        .OrderBy(c => c.CreatedAt)
                        .FirstOrDefault(x => x.CreatedAt > customerMessage.CreatedAt);

                    if (agentResponse != null)
                    {
                        countOfCustomerMessages++;
                        totalWaitTime += (agentResponse.CreatedAt - customerMessage.CreatedAt).TotalMinutes;
                    }
                }

                if (countOfCustomerMessages > 0)
                {
                    double averageWaitTime = totalWaitTime / countOfCustomerMessages;
                    responseTimes.Add(averageWaitTime);
                }
            }

            result.Add(new WaitTimeDataDto { Range = "<10m", Count = responseTimes.Count(rt => rt < 10) });
            result.Add(new WaitTimeDataDto { Range = "10-50m", Count = responseTimes.Count(rt => rt >= 10 && rt < 50) });
            result.Add(new WaitTimeDataDto { Range = "50-75m", Count = responseTimes.Count(rt => rt >= 50 && rt < 75) });
            result.Add(new WaitTimeDataDto { Range = "75-100m", Count = responseTimes.Count(rt => rt >= 75 && rt < 100) });
            result.Add(new WaitTimeDataDto { Range = ">100m", Count = responseTimes.Count(rt => rt >= 100) });

            return result;
        }



        private List<WaitTimeDataDto> CalculateWaitTimeForFirstAgentResponse(IEnumerable<Conversations> conversations, string agentPhoneNumber)
        {
            var result = new List<WaitTimeDataDto>();
            var firstResponses = new List<double>();

            var groupedConversations = conversations
                .GroupBy(c => new { c.From, c.To });

            foreach (var conversation in groupedConversations)
            {
                var customerMessages = conversations.Where(c => c.From.ToLower() != agentPhoneNumber && c.To.ToLower() == agentPhoneNumber)
                                                   .OrderBy(c => c.CreatedAt)
                                                   .ToList();

                var agentMessages = conversations.Where(c => c.From.ToLower() == agentPhoneNumber && c.To.ToLower() != agentPhoneNumber)
                                                .OrderBy(c => c.CreatedAt)
                                                .ToList();

                if (customerMessages.Any() && agentMessages.Any())
                {
                    var firstCustomerMessage = customerMessages.First();
                    var firstAgentResponse = agentMessages.FirstOrDefault(a => a.CreatedAt > firstCustomerMessage.CreatedAt);

                    if (firstAgentResponse != null)
                    {
                        var waitTime = (firstAgentResponse.CreatedAt - firstCustomerMessage.CreatedAt).TotalMinutes;
                        firstResponses.Add(waitTime);
                    }
                }
            }

            var timeRanges = new Dictionary<string, Func<double, bool>>
                {
                    { "<10m", wt => wt < 10 },
                    { "10-50m", wt => wt >= 10 && wt < 50 },
                    { "50-75m", wt => wt >= 50 && wt < 75 },
                    { "75-100m", wt => wt >= 75 && wt < 100 },
                    { ">100m", wt => wt >= 100 }
                };

            result.AddRange(timeRanges.Select(range => new WaitTimeDataDto
            {
                Range = range.Key,
                Count = firstResponses.Count(range.Value)
            }));
            return result;
        }
        #endregion
        #region Agent Performance Methods
        public List<AgentAvgResponseTimeDto> CalculateAgentResponseTime(IEnumerable<Conversations> conversations, string companyId)
        {

            // Agent Messages Query
            var agentMessagesQuery = conversations
                .Where(cs => cs.From.ToLowerInvariant() == companyId.ToLowerInvariant())
                .Select(cs => new
                {
                    cs.Id,
                    cs.From,
                    cs.To,
                    cs.CreatedAt,
                    cs.UserId
                })
                .OrderBy(cs => cs.CreatedAt)
                .ToList();

            // Customer Messages Query
            var customerMessagesQuery = conversations
                .Where(cs => cs.From != companyId && cs.To.ToLowerInvariant() == companyId.ToLowerInvariant())
                .Select(cs => new
                {
                    cs.Id,
                    cs.From,
                    cs.To,
                    cs.CreatedAt
                })
                .OrderBy(cs => cs.CreatedAt)
                .ToList();

            var responseTimesList = new List<dynamic>();

            var joinedMessages = from cm in customerMessagesQuery
                                 join am in agentMessagesQuery
                                     on new { From = cm.To.ToLowerInvariant(), To = cm.From.ToLowerInvariant() } equals new { am.From, am.To }
                                 where am.UserId != null && am.CreatedAt > cm.CreatedAt
                                 select new
                                 {
                                     CustomerMessage = cm,
                                     AgentMessage = am
                                 };

            var responsePairs = joinedMessages
                .GroupBy(jm => jm.CustomerMessage.Id)
                .Select(g => g.OrderBy(jm => jm.AgentMessage.CreatedAt).FirstOrDefault())
                .ToList();

            foreach (var pair in responsePairs)
            {
                if (pair != null)
                {
                    responseTimesList.Add(new
                    {
                        CustomerPhoneNumber = pair.CustomerMessage.From,
                        AgentId = pair.AgentMessage.UserId,
                        ResponseTime = CalculateDateDiffInMinutes(pair.CustomerMessage.CreatedAt, pair.AgentMessage.CreatedAt),
                        CCreatedAt = pair.CustomerMessage.CreatedAt,
                        ACreatedAt = pair.AgentMessage.CreatedAt
                    });
                }
            }

            var uniqueAgentResponses = new HashSet<(string CustomerPhoneNumber, string AgentId, DateTime ACreatedAt)>();
            var uniqueCustomerResponses = new HashSet<(string CustomerPhoneNumber, string AgentId, DateTime CCreatedAt)>();
            // Step 3: Use HashSet for Efficient Filtering
            var filteredResponses = new List<dynamic>();
            foreach (var message in responseTimesList)
            {
                var key = ((string)message.CustomerPhoneNumber, (string)message.AgentId, (DateTime)message.ACreatedAt);
                var customerKey = ((string)message.CustomerPhoneNumber, (string)message.AgentId, (DateTime)message.CCreatedAt);
                if (uniqueAgentResponses.Add(key) && uniqueCustomerResponses.Add(customerKey))
                {
                    filteredResponses.Add(message);
                }
            }
            var chatAvgResponseTimes = filteredResponses
                .GroupBy(rm => new { rm.AgentId, rm.CustomerPhoneNumber })
                .Select(grp => new
                {
                    AgentId = grp.Key.AgentId,
                    AvgResponseTimePerChat = grp.Average(rm => rm.ResponseTime)
                })
                .ToList();

            // Step 5: Calculate the Overall Average Response Time per Agent
            var overallAvgResponseTimes = chatAvgResponseTimes
                .GroupBy(car => car.AgentId)
                .Select(grp => new AgentAvgResponseTimeDto
                {
                    AgentId = Guid.Parse(grp.Key),
                    AvgResponseTime = (long)grp.Average(car => car.AvgResponseTimePerChat)
                })
                .ToList();

            return overallAvgResponseTimes;
        }
        public static int CalculateDateDiffInMinutes(DateTime startDate, DateTime endDate)
        {
            return (int)(endDate - startDate).TotalMinutes;
        }
        #endregion
        #endregion
    }
}
