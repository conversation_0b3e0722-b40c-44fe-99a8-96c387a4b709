using System.ComponentModel.DataAnnotations;

namespace EngagetoEntities.Dtos
{
    public class FirebaseDeviceRegistrationDto
    {
        [Required]
        [StringLength(500)]
        public string DeviceId { get; set; }

        [Required]
        [StringLength(1000)]
        public string FcmToken { get; set; }

        [StringLength(100)]
        public string? DeviceType { get; set; } = "Web";

        [StringLength(200)]
        public string? BrowserName { get; set; }

        [StringLength(100)]
        public string? BrowserVersion { get; set; }

        [StringLength(100)]
        public string? OperatingSystem { get; set; }

        [StringLength(50)]
        public string? TimeZone { get; set; }

        [StringLength(10)]
        public string? Language { get; set; }
    }

    public class FirebaseDeviceInfoUpdateDto
    {
        [StringLength(200)]
        public string? BrowserName { get; set; }

        [StringLength(100)]
        public string? BrowserVersion { get; set; }

        [StringLength(100)]
        public string? OperatingSystem { get; set; }

        [StringLength(50)]
        public string? TimeZone { get; set; }

        [StringLength(10)]
        public string? Language { get; set; }

        public bool? IsActive { get; set; }
    }

    public class FirebaseDeviceRegistrationResponseDto
    {
        public Guid Id { get; set; }
        public string DeviceId { get; set; }
        public string DeviceType { get; set; }
        public string? BrowserName { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public DateTime? LastUsedAt { get; set; }
    }

    public class FirebasePushNotificationDto
    {
        [Required]
        public string Title { get; set; }

        [Required]
        public string Body { get; set; }
        public string? Icon { get; set; }
        public string? Image { get; set; }
        public Dictionary<string, string>? Data { get; set; }
    }

    public class FirebasePushNotificationResponseDto
    {
        public bool Success { get; set; }
        public string? MessageId { get; set; }
        public string? Error { get; set; }
        public int TotalDevices { get; set; }
        public int SuccessfulDeliveries { get; set; }
        public int FailedDeliveries { get; set; }
    }
}
