﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.IntegrationDtos
{

    public class BaseIntegrationAccountDto
    {
        public string Name { get; set; }
        public SourceType Source { get; set; }
        public IntegrationAction Action { get; set; }
        public string? SubSource { get; set; }
        public string? APIEndpoint { get; set; }
        public string? APIKey { get; set; }
        public ApiMethod APIMethod { get; set; }
        public string? RawPayload { get; set; }
        public bool IsActive { get; set; }
        public DataFormat? AcceptedFormat { get; set; }
        public List<IntegrationEvent>? IntegrationEvents { get; set; }
        public Dictionary<string, string>? HeadersDict { get; set; }
        public HeaderParameterDto? HeaderParameterObj { get; set; }
        public Dictionary<ModelMapping, Dictionary<string, string>>? PayloadDict { get; set; }
        public List<PropertyMappingDto>? PropertyMappingList { get; set; }

    }

    public class IntegrationAccountDto : BaseIntegrationAccountDto
    {
        public Guid Id { get; set; }
        public string? BusinessId { get; set; }
        public Guid? UserId { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public Guid? CreatedBy { get; set; }
        public Guid? UpdatedBy { get; set; }
    }

    public class ViewIntegrationAccountDto : IntegrationAccountDto { }

    public class EditIntegrationAccountDto : IntegrationAccountDto { }
    public class IntegrationJobData
    {
        public Guid BusinessId { get; set; }
        public SourceType Source { get; set; }
        public string MethodName { get; set; } = string.Empty;
    }

}
