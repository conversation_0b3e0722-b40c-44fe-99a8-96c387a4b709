using System.ComponentModel.DataAnnotations;
using EngagetoEntities.Dtos.WorkflowDtos;

namespace EngagetoEntities.Dtos.MasterWorkflowDtos
{
    public class CreateMasterWorkflowDto
    {
        [Required]
        public string Name { get; set; }
        public string? Description { get; set; }
        [Required]
        public List<WorkflowNodeDto> Nodes { get; set; } = new List<WorkflowNodeDto>();
        [Required]
        public List<WorkflowEdgeDto> Edges { get; set; } = new List<WorkflowEdgeDto>();
    }

    public class MasterWorkflowResponseDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string? Description { get; set; }
        public bool IsActive { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public List<WorkflowNodeDto> Nodes { get; set; } = new List<WorkflowNodeDto>();
        public List<WorkflowEdgeDto> Edges { get; set; } = new List<WorkflowEdgeDto>();
    }

    public class CreateWorkflowFromMasterDto
    {
        [Required]
        public Guid MasterWorkflowId { get; set; }
        [Required]
        public Guid BusinessId { get; set; }
        [Required]
        public string WorkflowName { get; set; }
    }
}