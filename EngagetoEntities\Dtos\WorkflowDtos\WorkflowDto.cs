using System.ComponentModel.DataAnnotations;
using EngagetoEntities.Dtos.CommonDtos.PaymentGatwayDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.WorkflowDtos
{
    public class BaseWorkFlowDto
    {
        [Required]
        public string Name { get; set; }
        public  bool IsActive { get; set; } = false; 
    }
    public class CreateWorkflowDto : BaseWorkFlowDto
    {


    }
    public  class ViewWorkFlowDto: BaseWorkFlowDto
    {
       public Guid Id { get; set; }
      
    }


    public class UpdateWorkflowDto : BaseWorkFlowDto 
    {
        public List<WorkflowNodeDto>? Nodes { get; set; } = new List<WorkflowNodeDto>();
        public List<WorkflowEdgeDto>? Edges { get; set; } = new List<WorkflowEdgeDto>();
    }

    public class WorkflowDetailDto : BaseWorkFlowDto
    {
        public Guid Id { get; set; }
        public bool IsActive { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public List<WorkflowNodeDto> Nodes { get; set; } = new List<WorkflowNodeDto>();
        public List<WorkflowEdgeDto> Edges { get; set; } = new List<WorkflowEdgeDto>();
    }
    public class AddKeywordDto
    {
        public Guid WorkflowId { get; set; }
        public Guid WorkflowNodeId { get; set; }
        public List<string>? Keywords { get; set; } 
    }
    public class AddKeyWordResponseDto
    {
        public bool IsSuccess { get; set; }
        public string? Message { get; set; }
        public List<string>? FailedKeywords {  get; set; }
        public List<string>? AddedKeywords {  get; set; }
    }

}


