﻿using EngagetoEntities.Enums;
using System.ComponentModel.DataAnnotations;

namespace EngagetoEntities.Entities;

public class Contacts
{

    [Key]
    public Guid ContactId { get; set; }
    public string Name { get; set; } = default!;
    public Guid BusinessId { get; set; }
    public Guid? UserId { get; set; }
    public string CountryCode { get; set; } = default!;
    public string Contact { get; set; } = default!;
    public string? Email { get; set; }
    public string? CountryName { get; set; }
    public string? Tags { get; set; }
    public DateTime? CreatedDate { get; set; }
    public DateTime? UpdatedDate { get; set; }
    public DateTime? DeletedDate { get; set;}
    public bool IsDeleted { get; set; }
    public bool IsActive { get; set; }
    public string? Note { get; set; }
    public ChatStatus ChatStatus { get; set; }
    public string? DelayResponseJobID { get; set; }
    public bool? IsSpam { get; set; }
    public string? WorkflowName { get; set; }
    public int? WorkflowStep { get; set; }
    public long? WorkflowStartId { get; set; }
    public Is_OptIn IsOptIn { get; set; }
    public SourceType? Source { get; set; }
    public LeadSource? SubSource { get; set; }
    public string? SubCategory { get; set; }
    public DateTime? LastMessageAt { get; set; }
    public Guid? WorkFlowNodeId { get; set; }
    public Guid? WorkflowId { get; set; }
    public bool IsSent { get; set; } = false;
    public string? Project { get; set; }
    public string? LeadStatus { get; set; }
    public string? LeadSubStatus { get; set; }
    public DateTime? ScheduledAt { get; set; }
    public bool? IsBlock { get; set; } = false;
    public bool IsCompletedWorkflow { get; set; } = true;
}

