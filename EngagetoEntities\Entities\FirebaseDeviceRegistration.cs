using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("FirebaseDeviceRegistrations")]
    public class FirebaseDeviceRegistration
    {
        [Key]
        public Guid Id { get; set; }

        [Required]
        [StringLength(500)]
        public string DeviceId { get; set; }

        [Required]
        [StringLength(1000)]
        public string FcmToken { get; set; }

        [Required]
        public Guid UserId { get; set; }

        [Required]
        public Guid BusinessId { get; set; }

        [StringLength(100)]
        public string? DeviceType { get; set; } // Web, Android, iOS

        [StringLength(200)]
        public string? BrowserName { get; set; }

        [StringLength(100)]
        public string? BrowserVersion { get; set; }

        [StringLength(100)]
        public string? OperatingSystem { get; set; }

        [StringLength(50)]
        public string? TimeZone { get; set; }

        [StringLength(10)]
        public string? Language { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? LastUsedAt { get; set; }

    }
}
