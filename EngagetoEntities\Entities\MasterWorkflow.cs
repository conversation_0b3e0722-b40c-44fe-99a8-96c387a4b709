using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("MasterWorkflows")]
    public class MasterWorkflow : BaseEntity
    {
        [Key]
        [Required]
        public Guid Id { get; set; }
        [Required]
        public string Name { get; set; }
        public string? Description { get; set; }
        [Required]
        public string WorkflowJson { get; set; }
        public bool IsActive { get; set; } = true;
  
    }
}