﻿namespace EngagetoEntities.Utilities
{
    public static class MetaApi
    {
        private const string version23 = "v23.0";
        private const string BaseApiUrl = "https://graph.facebook.com";
        private const string CreateTemplateApiName = "message_templates";
        private const string CreateAuthenticationTemplateApiName = "upsert_message_templates";
        private const string SendMessageApiName = "messages";

        public static string GetBaseUrl() => $"{BaseApiUrl}/{version23}/";
        public static string GetCreateTemplateUrl(string businessNumber) => $"{BaseApiUrl}/{version23}/{businessNumber}/{CreateTemplateApiName}";
        public static string GetCreateAuthenticationTemplateUrl(string businessNumber) => $"{BaseApiUrl}/{version23}/{businessNumber}/{CreateAuthenticationTemplateApiName}";
        //  public static string GetCreateTemplateUrl(string businessNumber) => $"{BaseApiUrl}/{version21}/{businessNumber}/{CreateTemplateApiName}";
        public static string GetSendTemplateUrl(string businessNumber) => $"{BaseApiUrl}/{version23}/{businessNumber}/{SendMessageApiName}";
        public static string GetUpdateTemplateUrl(string templateId) => $"{BaseApiUrl}/{version23}/{templateId}";
        public static string GetHealthStatusUrl(string accountId) => $"{BaseApiUrl}/{version23}/{accountId}?fields=health_status";
        public static string GetMessageTemplatePreview(string accountId)
        {
            return $"{BaseApiUrl}/{version23}/{accountId}/message_template_previews";
        }
        public static string GetAccountDetailsUrl(string accountId) => $"{BaseApiUrl}/{version23}/{accountId}?fields=id,name,phone_numbers";
        public static string GetCreateAuthTemplateUrl(string accountId) => $"{BaseApiUrl}/{version23}/{accountId}/upsert_message_templates";
        public static string GetMetaTemplateUrl(string whatsappBusinessId) => $"{BaseApiUrl}/{version23}/{whatsappBusinessId}/message_templates";
        public static string GetMetaBlockUnblockUrl(string phoneNumberId) => $"{BaseApiUrl}/{version23}/{phoneNumberId}/block_users";
    }
}
