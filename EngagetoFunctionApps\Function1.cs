using EngagetoFunctionApps.Functions;
using EngagetoFunctionApps.Models;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Net;

namespace EngagetoFunctionApps
{
    public class Function1
    {
        [Function("JobScheduleRequest")]
        public static async Task<HttpResponseData> HttpStart(
            [HttpTrigger(AuthorizationLevel.Function, "post", Route = "JobScheduleRequest")] HttpRequestData req,
            [DurableClient] DurableTaskClient client,
             FunctionContext executionContext)
        {
            var log = executionContext.GetLogger("Processing ScheduleCampaign request.");
            var input = await req.ReadFromJsonAsync<InputPayload>();
            string instanceId = await client.ScheduleNewOrchestrationInstanceAsync(
                nameof(JobOrchestrator.RunCampaignOrchestrator),
                input);

            log.LogInformation($"Started orchestration with ID = '{instanceId}'.");

            return await client.CreateCheckStatusResponseAsync(req, instanceId);
        }


        [Function(nameof(ProcessSubBatches))]
        public static async Task<HttpResponseData> ProcessSubBatches(
           [HttpTrigger(AuthorizationLevel.Function, "post", Route = "ProcessSubBatches")] HttpRequestData req,
           [DurableClient] DurableTaskClient client,
            FunctionContext executionContext)
        {
            try
            {
                var json = await req.ReadAsStringAsync();
                var log = executionContext.GetLogger("Processing campaign sub batches request.");
                var input = JsonConvert.DeserializeObject<Payload>(json);
                string instanceId = await client.ScheduleNewOrchestrationInstanceAsync(
                    nameof(JobOrchestrator.ProcessEntityOrchestration),
                    input);

                log.LogInformation($"Started orchestration with ID = '{instanceId}'.");

                return await client.CreateCheckStatusResponseAsync(req, instanceId);
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        [Function("TerminateJob")]
        public static async Task<HttpResponseData> TerminatJob(
            [HttpTrigger(AuthorizationLevel.Function, "post", Route = "TerminatJob")] HttpRequestData req,
            [DurableClient] DurableTaskClient client,
            FunctionContext executionContext)
        {
            var log = executionContext.GetLogger("TerminatJob");
            var requestBody = await req.ReadFromJsonAsync<TerminateJobPayload>();

            if (string.IsNullOrEmpty(requestBody?.JobId))
            {
                return req.CreateResponse(HttpStatusCode.BadRequest);
            }

            try
            {
                await client.TerminateInstanceAsync(requestBody.JobId, "Terminated by user or business logic.");
                log.LogInformation($"Terminated orchestration with ID = '{requestBody.JobId}'.");
                return req.CreateResponse(HttpStatusCode.OK);
            }
            catch (Exception ex)
            {
                log.LogError(ex, $"Failed to terminate orchestration with ID = '{requestBody.JobId}'.");
                return req.CreateResponse(HttpStatusCode.InternalServerError);
            }
        }

    }
}
