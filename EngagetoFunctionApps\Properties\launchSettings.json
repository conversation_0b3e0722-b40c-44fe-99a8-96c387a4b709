{"profiles": {"EngagetoFunctionApps": {"commandName": "Project", "commandLineArgs": "--port 7157", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Container (Dockerfile)": {"commandName": "<PERSON>er", "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}", "containerRunArguments": "--init", "httpPort": 33409, "useSSL": false, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}