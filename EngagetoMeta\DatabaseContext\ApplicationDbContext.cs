﻿using EngagetoMeta.Entities;
using Microsoft.EntityFrameworkCore;


namespace EngagetoMeta.DatabaseContext
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<LogHistoryEntitity> LogHistoryEntities { get; set; }
        public DbSet<WAWebhookHistoryEntities> WAWebhookHistoryEntities { get; set; }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<LogHistoryEntitity>(entity =>
            {
                entity.ToTable("LogHistoryEntities");
                entity.HasKey(e => e.Id);
            });

            modelBuilder.Entity<WAWebhookHistoryEntities>(entity =>
            {
                entity.ToTable("WAWebhookHistoryEntities");
                entity.<PERSON><PERSON><PERSON>(e => e.Id);
            });
        }
    }
}
