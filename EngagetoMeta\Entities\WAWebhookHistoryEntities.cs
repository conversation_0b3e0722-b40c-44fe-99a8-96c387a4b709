﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoMeta.Entities
{
    [Table("WAWebhookHistoryEntities")]
    public class WAWebhookHistoryEntities
    {
        [Key]
        public Guid Id { get; set; }
        public string? WhatsAppBusinessAccountID { get; set; }
        public string? PhoneNumberID { get; set; }
        public string? WhatsAppMessageId { set; get; }
        public string? ResponseData { get; set; }
        public string? Field { get; set; }
        public string? WhatsAppStatus { get; set; }
        public DateTime CreatedAt { get; set; }

    }
}
