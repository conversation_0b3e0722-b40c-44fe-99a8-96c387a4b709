namespace EngagetoMeta.Repositories
{
    public interface IWAWebhookHistoryRepository
    {
        public Task<bool> SaveWebhookHistoryAsync(string? whatsAppBusinessAccountID, string? phoneNumberID, string? whatsAppMessageId, string? responseData, string? field, string? whatsAppStatus);
        public Task<bool> SaveMessageWebhookHistoryAsync(string? whatsAppBusinessAccountID, string? phoneNumberID, string? whatsAppMessageId, object? webhookData, string? status);
        public Task<bool> SaveTemplateStatusWebhookHistoryAsync(string? whatsAppBusinessAccountID, string? phoneNumberID, object? webhookData, string? status);
        public Task<bool> SaveBusinessCapabilityWebhookHistoryAsync(string? whatsAppBusinessAccountID, string? phoneNumberID, object? webhookData, long? messageLimit);
    }
}
