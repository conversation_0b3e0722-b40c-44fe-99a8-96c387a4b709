using EngagetoMeta.Repositories;
using EngagetoMeta.Dtos;
using Newtonsoft.Json;

namespace EngagetoMeta.Services
{
    public class WAWebhookHistoryService : IWAWebhookHistoryService
    {
        private readonly IWAWebhookHistoryRepository _waWebhookHistoryRepository;

        public WAWebhookHistoryService(IWAWebhookHistoryRepository waWebhookHistoryRepository)
        {
            _waWebhookHistoryRepository = waWebhookHistoryRepository;
        }
        public async Task<bool> ProcessAndSaveWebhookAsync(WebhookDto webhookDto, string? field)
        {
            try
            {
                var entry = webhookDto.Entry?[0];
                var change = entry?.Changes?[0];
                var value = change?.Value;
                string? businessAccountId = value?.Metadata?.WhatsappBusinessAccountId ?? entry?.Id;
                string? phoneNumberId = value?.Metadata?.PhoneNumberId;
                switch (field?.ToLower())
                {
                    case "messages":
                        return await ProcessMessagesWebhook(businessAccountId, phoneNumberId, value, webhookDto);

                    case "message_template_status_update":
                        return await _waWebhookHistoryRepository.SaveTemplateStatusWebhookHistoryAsync(
                            businessAccountId, phoneNumberId, webhookDto, "template_status_update");

                    case "business_capability_update":
                        return await _waWebhookHistoryRepository.SaveBusinessCapabilityWebhookHistoryAsync(
                            businessAccountId, phoneNumberId, webhookDto, value?.MessageLimitPerUser);

                    case "message_template_quality_update":
                        return await _waWebhookHistoryRepository.SaveWebhookHistoryAsync(
                            businessAccountId, phoneNumberId, null,
                            JsonConvert.SerializeObject(webhookDto),
                            field, "message_template_quality_update");

                    case "phone_number_quality_update":
                        return await _waWebhookHistoryRepository.SaveWebhookHistoryAsync(
                            businessAccountId, phoneNumberId, null,
                            JsonConvert.SerializeObject(webhookDto),
                            field, "phone_number_quality_update");

                    default:
                        return await _waWebhookHistoryRepository.SaveWebhookHistoryAsync(
                            businessAccountId, phoneNumberId, null,
                            JsonConvert.SerializeObject(webhookDto),
                            field ?? "unknown", null);
                }
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        private async Task<bool> ProcessMessagesWebhook(string? businessAccountId, string? phoneNumberId, Value? value, WebhookDto webhookDto)
        {
            bool result = true;
            try
            {
                if (value?.Messages != null && value.Messages.Any())
                {
                    foreach (var message in value.Messages)
                    {
                        var messageResult = await _waWebhookHistoryRepository.SaveMessageWebhookHistoryAsync(
                            businessAccountId, phoneNumberId, message.Id, webhookDto, "received");

                        if (!messageResult) result = false;
                    }
                }
                if (value?.Statuses != null && value.Statuses.Any())
                {
                    foreach (var status in value.Statuses)
                    {
                        var statusResult = await _waWebhookHistoryRepository.SaveMessageWebhookHistoryAsync(
                            businessAccountId, phoneNumberId, status.Id, webhookDto, status.Status);

                        if (!statusResult) result = false;
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
    }
}
