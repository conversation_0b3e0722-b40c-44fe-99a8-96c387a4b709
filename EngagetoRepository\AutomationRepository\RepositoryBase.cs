﻿using EngagetoContracts.AutomationContracts;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System.Reflection;
using System.Text.RegularExpressions;



namespace EngagetoRepository.AutomationRepository
{
    public class RepositoryBase : IRepositoryBase
    {
        private readonly IConfiguration _configuration;
        public readonly ApplicationDbContext dbContext;

        public RepositoryBase(ApplicationDbContext automationDb,
            
            IConfiguration configuration)
        {
            dbContext = automationDb;
            this._configuration = configuration;
        }
        public async Task<WorkingHours?> GetWorkingHours(Guid UserId, Guid BusinessId)
        {
            return await dbContext.WorkingHours.FirstOrDefaultAsync(m => m.BusinessId == BusinessId);
        }
        public bool IsValidTimeZone(string? timeZoneId)
        {
            try
            {
                if (timeZoneId == null)
                {
                    return false;
                }
                // Attempt to find the time zone by ID
                TimeZoneInfo timeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
                return true; // If no exception is thrown, the time zone is valid
            }
            catch (TimeZoneNotFoundException)
            {
                // The time zone ID is not found
                return false;
            }
            catch (InvalidTimeZoneException)
            {
                // The time zone is corrupted or invalid
                return false;
            }
        }
        public async Task<int> UpdateWorkingHours(WorkingHours data)
        {

            dbContext.Update(data);
            var status = await dbContext.SaveChangesAsync();

            return status;
        }
        public async Task AddWorkingHours(WorkingHours data)
        {

            await dbContext.AddRangeAsync(data);
            await dbContext.SaveChangesAsync();
        }

        public async Task InboxSettingsMessage(Guid UserId, Guid BusinessId, InboxSettingsMessageDto inboxSettingsMessageDto)
        {
            if (inboxSettingsMessageDto.Message != null)
            {
                Regex regex1 = new Regex(@"\{\{\d+\}\}");
                bool ContainsVariable = regex1.IsMatch(inboxSettingsMessageDto.Message);

                if (ContainsVariable && inboxSettingsMessageDto.Variables != null)
                {
                    int VariableCount = regex1.Matches(inboxSettingsMessageDto.Message).Count;
                    if (!(inboxSettingsMessageDto.Variables.Count().Equals(VariableCount)))
                    {
                        throw new Exception("Please check the variable count.");
                    }
                    foreach (var Variable in inboxSettingsMessageDto.Variables)
                    {
                        if (!regex1.IsMatch(Variable.Variable))
                        {
                            throw new Exception("Please check the variable.");
                        }
                        string[] columnNames = typeof(Contacts).GetProperties(BindingFlags.Public | BindingFlags.Instance).Select(p => p.Name).ToArray();
                        if (!(columnNames.Contains(Variable.Value)))
                        {
                            throw new Exception("Please check the variable value.");
                        }

                    }
                }
            }

            var InboxSettings = await dbContext.Templates.ToListAsync();
            var data = InboxSettings.Find(m => m.BusinessId.ToString() == (BusinessId.ToString()) && m.Feature == inboxSettingsMessageDto.Feature);
            if (data != null)
            {
                var Pre_Data = InboxSettings.Find(m => m.BusinessId.ToString() == BusinessId.ToString() && m.Feature == inboxSettingsMessageDto.Feature);
                if (Pre_Data != null)
                {
                    Pre_Data.UserId = UserId;
                    Pre_Data.Feature = inboxSettingsMessageDto.Feature;
                    Pre_Data.Body = inboxSettingsMessageDto.Message == null ? "" : inboxSettingsMessageDto.Message;
                    Pre_Data.Enabled = inboxSettingsMessageDto.Enabled;
                    Pre_Data.Delay = inboxSettingsMessageDto.Delay;
                    Pre_Data.MediaAwsUrl = inboxSettingsMessageDto.MediaUrl;

                    dbContext.Templates.Update(Pre_Data);
                    await dbContext.SaveChangesAsync();
                    var Variables = dbContext.InboxSettingsVariables.Where(m => m.TemplateId == Pre_Data.TemplateId);
                    if (Variables.Any())
                    {
                        dbContext.RemoveRange(Variables);
                        await dbContext.SaveChangesAsync();
                    }
                    if (inboxSettingsMessageDto.Variables != null)
                    {
                        List<InboxSettingsVariable> list = new List<InboxSettingsVariable>();
                        foreach (var variables in inboxSettingsMessageDto.Variables)
                        {
                            InboxSettingsVariable inboxSettingsVariables = new();
                            inboxSettingsVariables.FallBackValue = variables.Fallbackvalue;
                            inboxSettingsVariables.TemplateId = Pre_Data.TemplateId;
                            inboxSettingsVariables.Value = variables.Value;
                            inboxSettingsVariables.Variable = variables.Variable;
                            list.Add(inboxSettingsVariables);
                        }
                        await dbContext.AddRangeAsync(list);
                        await dbContext.SaveChangesAsync();
                    }
                }
            }
            else
            {

                Template settings = new Template();
                settings.TemplateId = Guid.NewGuid();
                settings.UserId = UserId;
                settings.MediaAwsUrl = inboxSettingsMessageDto.MediaUrl;
                settings.BusinessId = BusinessId.ToString();
                settings.Feature = inboxSettingsMessageDto.Feature;
                settings.Body = inboxSettingsMessageDto.Message == null ? "" : inboxSettingsMessageDto.Message;
                settings.Enabled = inboxSettingsMessageDto.Enabled;
                settings.Delay = inboxSettingsMessageDto.Delay;
                settings.Category = WATemplateCategory.MARKETING;
                settings.LanguageCode = "";
                settings.TemplateName = "";
                settings.SubCategory = "";

                var Entity = await dbContext.Templates.AddAsync(settings);
                await dbContext.SaveChangesAsync();
                if (inboxSettingsMessageDto.Variables != null)
                {
                    List<InboxSettingsVariable> list = new();
                    foreach (var variables in inboxSettingsMessageDto.Variables)
                    {
                        InboxSettingsVariable inboxSettingsVariables = new();
                        inboxSettingsVariables.FallBackValue = variables.Fallbackvalue;
                        inboxSettingsVariables.TemplateId = Entity.Entity.TemplateId;
                        inboxSettingsVariables.Value = variables.Value;
                        inboxSettingsVariables.Variable = variables.Variable;
                        list.Add(inboxSettingsVariables);
                    }
                    await dbContext.AddRangeAsync(list);
                    await dbContext.SaveChangesAsync();
                }
            }
        }
        public async Task<object> GetInboxSettingsMessage(Guid UserId, Guid BusinessId, Feature feature)
        {
            var InboxSettings = await dbContext.Templates.ToListAsync();
            var Features = InboxSettings.Where(m => m.BusinessId == BusinessId.ToString()).Select(m => m.Feature);
            var Pre_Data = new Template();

            if (Features.Contains(feature))
            {
                Pre_Data = InboxSettings.FirstOrDefault(m => m.BusinessId == BusinessId.ToString() && m.Feature == feature);

                if (Pre_Data != null)
                {


                    var Variables = dbContext.InboxSettingsVariables.Where(m => m.TemplateId == Pre_Data.TemplateId);
                    List<Variables> list = new List<Variables>();
                    if (Variables != null && await Variables.AnyAsync())
                    {

                        foreach (var variables in Variables)
                        {
                            Variables inboxSettingsVariables = new Variables();
                            inboxSettingsVariables.Fallbackvalue = variables.FallBackValue;
                            inboxSettingsVariables.Value = variables.Value;
                            inboxSettingsVariables.Variable = variables.Variable;
                            list.Add(inboxSettingsVariables);
                        }

                    }

                    var data = new
                    {
                        Pre_Data,
                        Variables = list
                    };
                    return data;
                }
                return new Template();
            }
            else
            {
                return Pre_Data;
            }
        }


        public async Task UpdateSelectResponseEntity(AutomationSelectResponseEntity selectResponseEntity)
        {
            dbContext.Update(selectResponseEntity);
            await dbContext.SaveChangesAsync();
        }
        public async Task<List<AutomationSelectResponseEntity>> GetSelectResponseEntity()
        {
            return await dbContext.AutomationSelectResponseEntities.ToListAsync();
        }
        public async Task AddSelectResponse(AutomationSelectResponseEntity selectResponse)
        {

            await dbContext.AutomationSelectResponseEntities.AddAsync(selectResponse);
            await dbContext.SaveChangesAsync();
        }
        public async Task DeleteSelectResponseAsync(Guid Id, Guid UserId)
        {
            var SelectResponse = await dbContext.AutomationSelectResponseEntities.FirstOrDefaultAsync(m => m.Id == Id);
            if (SelectResponse != null)
            {
                SelectResponse.DeletedBy = UserId;
                SelectResponse.IsDeleted = true;
                SelectResponse.DeletedAt = DateTime.UtcNow;
                dbContext.AutomationSelectResponseEntities.Update(SelectResponse);
                await dbContext.SaveChangesAsync();
            }
        }
        public async Task UpdateWorkflowResponseEntity(WorkflowResponseHistoryEntity selectResponseEntity)
        {
            dbContext.Update(selectResponseEntity);
            await dbContext.SaveChangesAsync();
        }
        public async Task<List<WorkflowResponseHistoryEntity>> GetWorkflowResponseEntity()
        {
            return await dbContext.WorkflowResponseHistoryEntities.Where(m => !m.IsDeleted).ToListAsync();
        }
        public async Task AddWorkflowResponse(WorkflowResponseHistoryEntity selectResponse)
        {

            await dbContext.WorkflowResponseHistoryEntities.AddAsync(selectResponse);
            await dbContext.SaveChangesAsync();
        }
        public async Task DeleteWorkflowResponseAsync(Guid Id, Guid UserId)
        {
            var SelectResponse = await dbContext.WorkflowResponseHistoryEntities.FirstOrDefaultAsync(m => m.Id == Id);
            if (SelectResponse != null)
            {
                SelectResponse.DeletedBy = UserId;
                SelectResponse.IsDeleted = true;
                SelectResponse.DeletedAt = DateTime.UtcNow;
                dbContext.WorkflowResponseHistoryEntities.Update(SelectResponse);
                await dbContext.SaveChangesAsync();
            }
        }
    }
}
