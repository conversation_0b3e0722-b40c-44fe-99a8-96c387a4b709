using EngagetoContracts.FirebaseContracts;
using FirebaseAdmin;
using Google.Apis.Auth.OAuth2;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace EngagetoRepository.FirebaseServices
{
    public class FirebaseConfigurationService : IFirebaseConfigurationService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<FirebaseConfigurationService> _logger;
        private FirebaseApp? _firebaseApp;
        private bool _isInitialized = false;

        public FirebaseConfigurationService(IConfiguration configuration, ILogger<FirebaseConfigurationService> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<bool> InitializeFirebaseAsync()
        {
            try
            {
                if (_isInitialized && _firebaseApp != null)
                {
                    return true;
                }

                var firebaseConfig = _configuration.GetSection("Firebase");
                var firebaseSettingConfig = _configuration.GetSection("FirebaseSetting");
                var projectId = firebaseConfig["ProjectId"] ?? firebaseSettingConfig["project_id"];

                if (string.IsNullOrEmpty(projectId))
                {
                    _logger.LogError("Firebase ProjectId is not configured");
                    return false;
                }

                GoogleCredential credential;

                // Try to load credentials from FirebaseSetting configuration
                if (firebaseSettingConfig.Exists())
                {
                    try
                    {
                        // Build Firebase credentials JSON from configuration
                        var firebaseCredentials = new
                        {
                            type = firebaseSettingConfig["type"],
                            project_id = firebaseSettingConfig["project_id"],
                            private_key_id = firebaseSettingConfig["private_key_id"],
                            private_key = firebaseSettingConfig["private_key"],
                            client_email = firebaseSettingConfig["client_email"],
                            client_id = firebaseSettingConfig["client_id"],
                            auth_uri = firebaseSettingConfig["auth_uri"],
                            token_uri = firebaseSettingConfig["token_uri"],
                            auth_provider_x509_cert_url = firebaseSettingConfig["auth_provider_x509_cert_url"],
                            client_x509_cert_url = firebaseSettingConfig["client_x509_cert_url"],
                            universe_domain = firebaseSettingConfig["universe_domain"]
                        };

                        var firebaseSettingJson = System.Text.Json.JsonSerializer.Serialize(firebaseCredentials);

                        _logger.LogInformation("Attempting to load Firebase credentials from FirebaseSetting configuration");
                        _logger.LogDebug("JSON content length: {Length}", firebaseSettingJson.Length);

                        credential = GoogleCredential.FromJson(firebaseSettingJson);
                        _logger.LogInformation("Firebase credentials loaded from FirebaseSetting configuration successfully");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to load Firebase credentials from FirebaseSetting configuration");
                        return false;
                    }
                }
                else
                {
                    // Try to use default credentials (for production environments)
                    try
                    {
                        credential = GoogleCredential.GetApplicationDefault();
                        _logger.LogInformation("Firebase credentials loaded from application default");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to load Firebase credentials. No valid credential source found");
                        return false;
                    }
                }

                var options = new AppOptions()
                {
                    Credential = credential,
                    ProjectId = projectId
                };

                // Check if a default Firebase app already exists
                try
                {
                    _firebaseApp = FirebaseApp.DefaultInstance;
                    if (_firebaseApp != null)
                    {
                        _logger.LogInformation("Using existing Firebase app instance");
                        _isInitialized = true;
                        return true;
                    }
                }
                catch (InvalidOperationException)
                {
                    // No default instance exists, create a new one
                    _logger.LogInformation("Creating new Firebase app instance");
                }

                _firebaseApp = FirebaseApp.Create(options);
                _isInitialized = true;

                _logger.LogInformation("Firebase initialized successfully for project: {ProjectId}", projectId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize Firebase");
                return false;
            }
        }

        public FirebaseApp GetFirebaseApp()
        {
            if (!_isInitialized || _firebaseApp == null)
            {
                throw new InvalidOperationException("Firebase is not initialized. Call InitializeFirebaseAsync first.");
            }

            return _firebaseApp;
        }

        public bool IsFirebaseInitialized()
        {
            return _isInitialized && _firebaseApp != null;
        } 
    }
}
