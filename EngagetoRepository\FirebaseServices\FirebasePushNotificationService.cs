using EngagetoContracts.FirebaseContracts;
using EngagetoEntities.Dtos;
using EngagetoEntities.Entities;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using FirebaseAdmin.Messaging;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Mapster;

namespace EngagetoRepository.FirebaseServices
{
    public class FirebasePushNotificationService : IFirebasePushNotificationService
    {
        private readonly IFirebaseConfigurationService _firebaseConfig;
        private readonly IGenericRepository _genericRepository;
        private readonly ILogger<FirebasePushNotificationService> _logger;
        private readonly IConfiguration _configuration;

        public FirebasePushNotificationService(
            IFirebaseConfigurationService firebaseConfig,
            IGenericRepository genericRepository,
            ILogger<FirebasePushNotificationService> logger,
            IConfiguration configuration)
        {
            _firebaseConfig = firebaseConfig;
            _genericRepository = genericRepository;
            _logger = logger;
            _configuration = configuration;
        }

        public async Task<FirebaseDeviceRegistrationResponseDto> RegisterDeviceAsync(
            FirebaseDeviceRegistrationDto deviceDto,
            Guid userId,
            Guid businessId)
        {
            try
            {
                // Check if device already exists
                var existingDevice = await _genericRepository.GetByObjectAsync<FirebaseDeviceRegistration>(
                    new Dictionary<string, object>
                    {
                        { "DeviceId", deviceDto.DeviceId },
                        { "UserId", userId },
                        { "BusinessId", businessId }
                    });

                var device = existingDevice.FirstOrDefault();

                if (device != null)
                {
                    // Update existing device
                    device.FcmToken = deviceDto.FcmToken;
                    device.DeviceType = deviceDto.DeviceType;
                    device.BrowserName = deviceDto.BrowserName;
                    device.BrowserVersion = deviceDto.BrowserVersion;
                    device.OperatingSystem = deviceDto.OperatingSystem;
                    device.TimeZone = deviceDto.TimeZone;
                    device.Language = deviceDto.Language;
                    device.UpdatedAt = DateTime.UtcNow;
                    device.LastUsedAt = DateTime.UtcNow;
                    device.IsActive = true;

                    await _genericRepository.UpdateRecordAsync(device, new Dictionary<string, object> { { "Id", device.Id } });
                    _logger.LogInformation("Updated existing Firebase device registration for DeviceId: {DeviceId}", deviceDto.DeviceId);
                }
                else
                {
                    // Create new device registration
                    device = new FirebaseDeviceRegistration
                    {
                        Id = Guid.NewGuid(),
                        DeviceId = deviceDto.DeviceId,
                        FcmToken = deviceDto.FcmToken,
                        UserId = userId,
                        BusinessId = businessId,
                        DeviceType = deviceDto.DeviceType,
                        BrowserName = deviceDto.BrowserName,
                        BrowserVersion = deviceDto.BrowserVersion,
                        OperatingSystem = deviceDto.OperatingSystem,
                        TimeZone = deviceDto.TimeZone,
                        Language = deviceDto.Language,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                        LastUsedAt = DateTime.UtcNow
                    };

                    await _genericRepository.SaveAsync(device);
                    _logger.LogInformation("Created new Firebase device registration for DeviceId: {DeviceId}", deviceDto.DeviceId);
                }

                return device.Adapt<FirebaseDeviceRegistrationResponseDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to register Firebase device for DeviceId: {DeviceId}", deviceDto.DeviceId);
                throw;
            }
        }

        public async Task<bool> RemoveDeviceAsync(string deviceId, Guid userId, Guid businessId)
        {
            try
            {
                var device = await _genericRepository.GetByObjectAsync<FirebaseDeviceRegistration>(
                    new Dictionary<string, object>
                    {
                        { "DeviceId", deviceId },
                        { "UserId", userId },
                        { "BusinessId", businessId }
                    });

                var deviceToRemove = device.FirstOrDefault();
                if (deviceToRemove != null)
                {
                    await _genericRepository.DeleteRecordAsync<FirebaseDeviceRegistration>("FirebaseDeviceRegistrations",
                        new List<EngagetoDapper.Data.Dtos.RequestFilterDto>
                        {
                            new EngagetoDapper.Data.Dtos.RequestFilterDto { Key = "Id", Value = deviceToRemove.Id, Operator = "=" }
                        });
                    _logger.LogInformation("Removed Firebase device registration for DeviceId: {DeviceId}", deviceId);
                    return true;
                }

                _logger.LogWarning("Firebase device not found for removal. DeviceId: {DeviceId}", deviceId);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to remove Firebase device for DeviceId: {DeviceId}", deviceId);
                throw;
            }
        }

        public async Task<FirebaseDeviceRegistrationResponseDto?> UpdateDeviceInfoAsync(
            string deviceId,
            FirebaseDeviceInfoUpdateDto updateDto,
            Guid userId,
            Guid businessId)
        {
            try
            {
                var devices = await _genericRepository.GetByObjectAsync<FirebaseDeviceRegistration>(
                    new Dictionary<string, object>
                    {
                        { "DeviceId", deviceId },
                        { "UserId", userId },
                        { "BusinessId", businessId }
                    });

                var device = devices.FirstOrDefault();
                if (device == null)
                {
                    _logger.LogWarning("Firebase device not found for update. DeviceId: {DeviceId}", deviceId);
                    return null;
                }

                // Update only provided fields
                if (!string.IsNullOrEmpty(updateDto.BrowserName))
                    device.BrowserName = updateDto.BrowserName;

                if (!string.IsNullOrEmpty(updateDto.BrowserVersion))
                    device.BrowserVersion = updateDto.BrowserVersion;

                if (!string.IsNullOrEmpty(updateDto.OperatingSystem))
                    device.OperatingSystem = updateDto.OperatingSystem;

                if (!string.IsNullOrEmpty(updateDto.TimeZone))
                    device.TimeZone = updateDto.TimeZone;

                if (!string.IsNullOrEmpty(updateDto.Language))
                    device.Language = updateDto.Language;

                if (updateDto.IsActive.HasValue)
                    device.IsActive = updateDto.IsActive.Value;

                device.UpdatedAt = DateTime.UtcNow;
                device.LastUsedAt = DateTime.UtcNow;

                await _genericRepository.UpdateRecordAsync(device, new Dictionary<string, object> { { "Id", device.Id } });
                _logger.LogInformation("Updated Firebase device info for DeviceId: {DeviceId}", deviceId);

                return device.Adapt<FirebaseDeviceRegistrationResponseDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update Firebase device info for DeviceId: {DeviceId}", deviceId);
                throw;
            }
        }

        public async Task<List<FirebaseDeviceRegistrationResponseDto>> GetUserDevicesAsync(Guid userId, Guid businessId)
        {
            try
            {
                var devices = await _genericRepository.GetByObjectAsync<FirebaseDeviceRegistration>(
                    new Dictionary<string, object>
                    {
                        { "UserId", userId },
                        { "BusinessId", businessId },
                        { "IsActive", true }
                    });

                return devices.Adapt<List<FirebaseDeviceRegistrationResponseDto>>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get user devices for UserId: {UserId}", userId);
                throw;
            }
        }

        public async Task<FirebasePushNotificationResponseDto> SendNotificationToUserAsync(
            Guid userId,
            Guid businessId,
            FirebasePushNotificationDto notification)
        {
            try
            {
                var devices = await _genericRepository.GetByObjectAsync<FirebaseDeviceRegistration>(
                    new Dictionary<string, object>
                    {
                        { "UserId", userId },
                        { "BusinessId", businessId },
                        { "IsActive", true }
                    });

                if (!devices.Any())
                {
                    _logger.LogWarning("No active devices found for user: {UserId}", userId);
                    return new FirebasePushNotificationResponseDto
                    {
                        Success = false,
                        Error = "No active devices found for user",
                        TotalDevices = 0,
                        SuccessfulDeliveries = 0,
                        FailedDeliveries = 0
                    };
                }

                // Send notification to all device tokens
                int totalDevices = devices.Count;
                int successfulDeliveries = 0;
                int failedDeliveries = 0;
                var errors = new List<string>();
                string? lastSuccessfulMessageId = null;

                _logger.LogInformation("Sending notifications to {DeviceCount} devices for user {UserId}", totalDevices, userId);

                foreach (var device in devices)
                {
                    try
                    {
                        var token = device.FcmToken;
                        _logger.LogDebug("Sending notification to device {DeviceId} with token: {TokenPrefix}...",
                            device.DeviceId, token.Substring(0, Math.Min(10, token.Length)));

                        var result = await SendNotificationToTokensAsync(token, notification);

                        if (result.Success)
                        {
                            successfulDeliveries++;
                            lastSuccessfulMessageId = result.MessageId;
                            _logger.LogDebug("Successfully sent notification to device {DeviceId}", device.DeviceId);
                        }
                        else
                        {
                            failedDeliveries++;
                            errors.Add($"Device {device.DeviceId}: {result.Error}");
                            _logger.LogWarning("Failed to send notification to device {DeviceId}: {Error}", device.DeviceId, result.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        failedDeliveries++;
                        errors.Add($"Device {device.DeviceId}: {ex.Message}");
                        _logger.LogError(ex, "Exception while sending notification to device {DeviceId}", device.DeviceId);
                    }
                }

                return new FirebasePushNotificationResponseDto
                {
                    Success = successfulDeliveries > 0,
                    TotalDevices = totalDevices,
                    SuccessfulDeliveries = successfulDeliveries,
                    FailedDeliveries = failedDeliveries,
                    MessageId = lastSuccessfulMessageId,
                    Error = errors.Any() ? string.Join("; ", errors) : null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send notification to user: {UserId}", userId);
                return new FirebasePushNotificationResponseDto
                {
                    Success = false,
                    Error = ex.Message,
                    TotalDevices = 0,
                    SuccessfulDeliveries = 0,
                    FailedDeliveries = 0
                };
            }
        }
        public async Task<FirebasePushNotificationResponseDto> SendNotificationToBusinessUsersAsync(
            Guid businessId,
            FirebasePushNotificationDto notification)
        {
            try
            {
                var devices = await _genericRepository.GetByObjectAsync<FirebaseDeviceRegistration>(
                    new Dictionary<string, object>
                    {
                        { "BusinessId", businessId },
                        { "IsActive", true }
                    });

                if (!devices.Any())
                {
                    _logger.LogWarning("No active devices found for business: {BusinessId}", businessId);
                    return new FirebasePushNotificationResponseDto
                    {
                        Success = false,
                        Error = "No active devices found for business",
                        TotalDevices = 0,
                        SuccessfulDeliveries = 0,
                        FailedDeliveries = 0
                    };
                }

                // Send notification to all device tokens for business users
                int totalDevices = devices.Count;
                int successfulDeliveries = 0;
                int failedDeliveries = 0;
                var errors = new List<string>();
                string? lastSuccessfulMessageId = null;

                _logger.LogInformation("Sending notifications to {DeviceCount} devices for business {BusinessId}", totalDevices, businessId);

                foreach (var device in devices)
                {
                    try
                    {
                        var token = device.FcmToken;
                        _logger.LogDebug("Sending notification to business device {DeviceId} with token: {TokenPrefix}...",
                            device.DeviceId, token.Substring(0, Math.Min(10, token.Length)));

                        var result = await SendNotificationToTokensAsync(token, notification);

                        if (result.Success)
                        {
                            successfulDeliveries++;
                            lastSuccessfulMessageId = result.MessageId;
                            _logger.LogDebug("Successfully sent notification to business device {DeviceId}", device.DeviceId);
                        }
                        else
                        {
                            failedDeliveries++;
                            errors.Add($"Device {device.DeviceId}: {result.Error}");
                            _logger.LogWarning("Failed to send notification to business device {DeviceId}: {Error}", device.DeviceId, result.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        failedDeliveries++;
                        errors.Add($"Device {device.DeviceId}: {ex.Message}");
                        _logger.LogError(ex, "Exception while sending notification to business device {DeviceId}", device.DeviceId);
                    }
                }

                return new FirebasePushNotificationResponseDto
                {
                    Success = successfulDeliveries > 0,
                    TotalDevices = totalDevices,
                    SuccessfulDeliveries = successfulDeliveries,
                    FailedDeliveries = failedDeliveries,
                    MessageId = lastSuccessfulMessageId,
                    Error = errors.Any() ? string.Join("; ", errors) : null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send notification to business users: {BusinessId}", businessId);
                return new FirebasePushNotificationResponseDto
                {
                    Success = false,
                    Error = ex.Message,
                    TotalDevices = 0,
                    SuccessfulDeliveries = 0,
                    FailedDeliveries = 0
                };
            }
        }

        public async Task<bool> ValidateTokenAsync(string fcmToken)
        {
            try
            {
                if (!_firebaseConfig.IsFirebaseInitialized())
                {
                    await _firebaseConfig.InitializeFirebaseAsync();
                }

                var message = new Message()
                {
                    Token = fcmToken,
                    Data = new Dictionary<string, string>
                    {
                        { "test", "validation" }
                    }
                };

                // Try to send a dry run message to validate the token
                var messaging = FirebaseMessaging.GetMessaging(_firebaseConfig.GetFirebaseApp());
                await messaging.SendAsync(message, dryRun: true);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "FCM token validation failed for token: {Token}", fcmToken);
                return false;
            }
        }

    
        public async Task<FirebasePushNotificationResponseDto> SendNotificationToTokensAsync(
            string token,
            FirebasePushNotificationDto notification)
        {
            try
            {
                // Validate inputs
                if (string.IsNullOrWhiteSpace(token))
                {
                    _logger.LogWarning("No FCM token provided for notification");
                    return new FirebasePushNotificationResponseDto
                    {
                        Success = false,
                        Error = "No FCM token provided",
                        TotalDevices = 0,
                        SuccessfulDeliveries = 0,
                        FailedDeliveries = 1
                    };
                }

                // Basic token validation
                if (token.Length < 100)
                {
                    _logger.LogWarning("FCM token appears to be too short: {TokenLength} characters", token.Length);
                    return new FirebasePushNotificationResponseDto
                    {
                        Success = false,
                        Error = "Invalid FCM token format",
                        TotalDevices = 1,
                        SuccessfulDeliveries = 0,
                        FailedDeliveries = 1
                    };
                }

                _logger.LogInformation("Attempting to send FCM notification to single token");

                if (!_firebaseConfig.IsFirebaseInitialized())
                {
                    _logger.LogInformation("Firebase not initialized, initializing now...");
                    var initResult = await _firebaseConfig.InitializeFirebaseAsync();
                    if (!initResult)
                    {
                        throw new InvalidOperationException("Failed to initialize Firebase");
                    }
                }

                var firebaseApp = _firebaseConfig.GetFirebaseApp();
                if (firebaseApp == null)
                {
                    throw new InvalidOperationException("Firebase app is not initialized");
                }

                var messaging = FirebaseMessaging.GetMessaging(firebaseApp);

                // Create notification message for single token
                var message = await CreateSingleFirebaseMessageAsync(token, notification);

                _logger.LogInformation("Sending FCM notification with title: '{Title}' to single token", notification.Title);

                var response = await messaging.SendAsync(message);

                _logger.LogInformation("FCM notification sent successfully: {MessageId}", response);

                return new FirebasePushNotificationResponseDto
                {
                    Success = true,
                    TotalDevices = 1,
                    SuccessfulDeliveries = 1,
                    FailedDeliveries = 0,
                    MessageId = response
                };
            }
            catch (FirebaseMessagingException fmEx)
            {
                _logger.LogError(fmEx, "Firebase Messaging error: {ErrorCode} - {Message}",
                    fmEx.ErrorCode, fmEx.Message);
                return new FirebasePushNotificationResponseDto
                {
                    Success = false,
                    Error = $"Firebase error: {fmEx.ErrorCode} - {fmEx.Message}",
                    TotalDevices = 1,
                    SuccessfulDeliveries = 0,
                    FailedDeliveries = 1
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send FCM notification to token");
                return new FirebasePushNotificationResponseDto
                {
                    Success = false,
                    Error = ex.Message,
                    TotalDevices = 1,
                    SuccessfulDeliveries = 0,
                    FailedDeliveries = 1
                };
            }
        }
        private async Task<Message> CreateSingleFirebaseMessageAsync(
            string token,
            FirebasePushNotificationDto notification)
        {
            var webpushConfig = new WebpushConfig()
            {
                Notification = new WebpushNotification()
                {
                    Title = notification.Title,
                    Body = notification.Body

                }
            };
            var message = new Message()
            {
                Token = token,
                Webpush = webpushConfig,
                // Data = messageData
            };

            return await Task.FromResult(message);
        }

        
    }
}
