using EngagetoContracts.FirebaseContracts;
using EngagetoEntities.Dtos;
using EngagetoEntities.Dtos.WebhookDtos;
using EngagetoEntities.Entities;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using Microsoft.Extensions.Logging;

namespace EngagetoRepository.FirebaseServices
{
    public class WhatsAppFirebaseNotificationService : IWhatsAppFirebaseNotificationService
    {
        private readonly IFirebasePushNotificationService _firebaseService;
        private readonly IGenericRepository _genericRepository;
        private readonly ILogger<WhatsAppFirebaseNotificationService> _logger;

        public WhatsAppFirebaseNotificationService(
            IFirebasePushNotificationService firebaseService,
            IGenericRepository genericRepository,
            ILogger<WhatsAppFirebaseNotificationService> logger)
        {
            _firebaseService = firebaseService;
            _genericRepository = genericRepository;
            _logger = logger;
        }

        public async Task SendWhatsAppMessageNotificationAsync(WAWebhookDto webhookDto)
        {
            try
            {
                var changes = webhookDto.Entry?[0]?.Changes?[0];
                var field = changes?.Field;
                
                if (field != "messages")
                {
                    return; // Only process incoming messages
                }

                var value = changes.Value;
                var metadata = value?.Metadata;
                var contacts = value?.Contacts;
                var messages = value?.Messages;

                if (metadata == null || contacts == null || messages == null || !messages.Any())
                {
                    _logger.LogWarning("Invalid WhatsApp webhook data for Firebase notification");
                    return;
                }

                var phoneNumberId = metadata.PhoneNumberId;
                var contact = contacts.FirstOrDefault();
                var message = messages.FirstOrDefault();

                if (contact == null || message == null)
                {
                    return;
                }

                // Get business details from phone number ID
                var businessMeta = await _genericRepository.GetByObjectAsync<BusinessDetailsMeta>(
                    new Dictionary<string, object> { { "PhoneNumberID", phoneNumberId } });

                var businessMetaData = businessMeta.FirstOrDefault();
                if (businessMetaData == null)
                {
                    _logger.LogWarning("Business not found for phone number ID: {PhoneNumberId}", phoneNumberId);
                    return;
                }

                if (string.IsNullOrEmpty(businessMetaData.BusinessId) || !Guid.TryParse(businessMetaData.BusinessId, out var businessId))
                {
                    _logger.LogWarning("Invalid or missing business ID: {BusinessId}", businessMetaData.BusinessId);
                    return;
                }

                var notification = new FirebasePushNotificationDto
                {
                    Title = contact.WaId.ToString() ??  string.Empty,
                    Body = message.Text.Body.ToString() ??  string.Empty,
                };

                // Send notification to all business users

                var result = await _firebaseService.SendNotificationToBusinessUsersAsync(businessId, notification);

                if (result.Success)
                {
                    _logger.LogInformation("Firebase notification sent for WhatsApp message. BusinessId: {BusinessId}, Sender: {Sender}, Devices: {DeviceCount}",
                        businessId, contact.WaId, result.SuccessfulDeliveries);
                }
                else
                {
                    _logger.LogWarning("Failed to send Firebase notification for WhatsApp message. BusinessId: {BusinessId}, Error: {Error}",
                        businessId, result.Error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending Firebase notification for WhatsApp message");
            }
        }
   
    }
}
