using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using EngagetoContracts.Services;
using EngagetoContracts.Workflow;
using EngagetoEntities.Dtos.MasterWorkflowDtos;
using EngagetoEntities.Dtos.WorkflowDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.DdContext;
using DocumentFormat.OpenXml.Wordprocessing;

namespace EngagetoRepository.Services
{
    public class MasterWorkflowService : IMasterWorkflowService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IWorkflowService _workflowService;
        private readonly IUserIdentityService _userIdentityService; 

        public MasterWorkflowService(ApplicationDbContext dbContext, IWorkflowService workflowService,   IUserIdentityService userIdentityService)
        {
            _dbContext = dbContext;
            _workflowService = workflowService;
            _userIdentityService = userIdentityService;
        }

        public async Task<MasterWorkflowResponseDto> CreateMasterWorkflowAsync(CreateMasterWorkflowDto masterWorkflow)
        {
            var existingWorkflow = await _dbContext.MasterWorkflows
                .FirstOrDefaultAsync(w => w.Name == masterWorkflow.Name && !w.IsDeleted);

            if (existingWorkflow != null)
            {
                throw new Exception("Master workflow with this name already exists");
            }

            var workflowData = new
            {
                Nodes = masterWorkflow.Nodes,
                Edges = masterWorkflow.Edges
            };

            var newMasterWorkflow = new MasterWorkflow
            {
                Id = Guid.NewGuid(),
                Name = masterWorkflow.Name,
                Description = masterWorkflow.Description,
                WorkflowJson = JsonConvert.SerializeObject(workflowData),
                IsActive = true,
                CreatedBy = _userIdentityService.UserId,
                CreatedAt = DateTime.UtcNow
            };

            _dbContext.MasterWorkflows.Add(newMasterWorkflow);
            await _dbContext.SaveChangesAsync();

            return new MasterWorkflowResponseDto
            {
                Id = newMasterWorkflow.Id,
                Name = newMasterWorkflow.Name,
                Description = newMasterWorkflow.Description,
                IsActive = newMasterWorkflow.IsActive,
                CreatedAt = newMasterWorkflow.CreatedAt,
                Nodes = masterWorkflow.Nodes,
                Edges = masterWorkflow.Edges
            };
        }

        public async Task<ViewWorkFlowDto> CreateWorkflowFromMasterAsync(CreateWorkflowFromMasterDto request)
        {
            var masterWorkflow = await _dbContext.MasterWorkflows
                .FirstOrDefaultAsync(w => w.Id == request.MasterWorkflowId && !w.IsDeleted);

            if (masterWorkflow == null)
            {
                throw new Exception("Master workflow not found");
            }

            var workflowData = JsonConvert.DeserializeObject<dynamic>(masterWorkflow.WorkflowJson);

            // Create the basic workflow first
            var createWorkflowDto = new CreateWorkflowDto
            {
                Name = request.WorkflowName,
                IsActive = false
            };

            var createdWorkflow = await _workflowService.CreateWorkflowAsync(createWorkflowDto,_userIdentityService.UserId);

            // Now update the workflow with nodes and edges from master workflow
            if (workflowData?.Nodes != null && workflowData?.Edges != null)
            {
                var originalNodes = JsonConvert.DeserializeObject<List<WorkflowNodeDto>>(workflowData.Nodes.ToString());
                var originalEdges = JsonConvert.DeserializeObject<List<WorkflowEdgeDto>>(workflowData.Edges.ToString());

                // Create mapping of old node IDs to new node IDs
                var nodeIdMapping = new Dictionary<Guid, Guid>();
                var newNodes = new List<WorkflowNodeDto>();

                // Generate new IDs for all nodes
                foreach (var node in originalNodes)
                {
                    var newNodeId = Guid.NewGuid();
                    nodeIdMapping[node.Id] = newNodeId;

                    var newNode = new WorkflowNodeDto
                    {
                        Id = newNodeId,
                        Type = node.Type,
                        IsEntry = node.IsEntry,
                        IsFinal = node.IsFinal,
                        Data = node.Data,
                        AttributeId = node.AttributeId,
                        PositionX = node.PositionX,
                        PositionY = node.PositionY
                    };
                    newNodes.Add(newNode);
                }

                // Update edges with new node IDs
                var newEdges = new List<WorkflowEdgeDto>();
                foreach (var edge in originalEdges)
                {
                    var newEdge = new WorkflowEdgeDto
                    {
                        Id = Guid.NewGuid(),
                        SourceNodeId = nodeIdMapping.ContainsKey(edge.SourceNodeId) ? nodeIdMapping[edge.SourceNodeId] : edge.SourceNodeId,
                        SourceHandle = edge.SourceHandle,
                        TargetHandle = edge.TargetHandle,
                        Type = edge.Type,
                        Targets = new List<WorkflowEdgeTargetDto>()
                    };

                    // Update target node IDs
                    if (edge.Targets != null)
                    {
                        foreach (var target in edge.Targets)
                        {
                            var newTarget = new WorkflowEdgeTargetDto
                            {
                                TargetNodeId = nodeIdMapping.ContainsKey(target.TargetNodeId) ? nodeIdMapping[target.TargetNodeId] : target.TargetNodeId,
                                Condition = target.Condition
                            };
                            newEdge.Targets.Add(newTarget);
                        }
                    }
                    newEdges.Add(newEdge);
                }

                var updateWorkflowDto = new UpdateWorkflowDto
                {
                    Name = request.WorkflowName,
                    IsActive = createWorkflowDto.IsActive,
                    Nodes = newNodes,
                    Edges = newEdges
                };

                await _workflowService.UpdateWorkflowAsync(createdWorkflow.Id, updateWorkflowDto);
            }

            return createdWorkflow;
        }

        public async Task<List<MasterWorkflowResponseDto>> GetAllMasterWorkflowsAsync()
        {
            var masterWorkflows = await _dbContext.MasterWorkflows
                .Where(w => !w.IsDeleted && w.IsActive)
                .OrderByDescending(w => w.CreatedAt)
                .ToListAsync();

            var result = new List<MasterWorkflowResponseDto>();

            foreach (var workflow in masterWorkflows)
            {
                var workflowData = JsonConvert.DeserializeObject<dynamic>(workflow.WorkflowJson);

                result.Add(new MasterWorkflowResponseDto
                {
                    Id = workflow.Id,
                    Name = workflow.Name,
                    Description = workflow.Description,
                    IsActive = workflow.IsActive,
                    CreatedAt = workflow.CreatedAt,
                    UpdatedAt = workflow.UpdatedAt,
                    Nodes = workflowData?.Nodes != null ? JsonConvert.DeserializeObject<List<WorkflowNodeDto>>(workflowData.Nodes.ToString()) : new List<WorkflowNodeDto>(),
                    Edges = workflowData?.Edges != null ? JsonConvert.DeserializeObject<List<WorkflowEdgeDto>>(workflowData.Edges.ToString()) : new List<WorkflowEdgeDto>()
                });
            }

            return result;
        }

        public async Task<MasterWorkflowResponseDto> GetMasterWorkflowByIdAsync(Guid id)
        {
            var masterWorkflow = await _dbContext.MasterWorkflows
                .FirstOrDefaultAsync(w => w.Id == id && !w.IsDeleted);

            if (masterWorkflow == null)
            {
                throw new Exception("Master workflow not found");
            }

            var workflowData = JsonConvert.DeserializeObject<dynamic>(masterWorkflow.WorkflowJson);

            return new MasterWorkflowResponseDto
            {
                Id = masterWorkflow.Id,
                Name = masterWorkflow.Name,
                Description = masterWorkflow.Description,
                IsActive = masterWorkflow.IsActive,
                CreatedAt = masterWorkflow.CreatedAt,
                UpdatedAt = masterWorkflow.UpdatedAt,
                Nodes = workflowData?.Nodes != null ? JsonConvert.DeserializeObject<List<WorkflowNodeDto>>(workflowData.Nodes.ToString()) : new List<WorkflowNodeDto>(),
                Edges = workflowData?.Edges != null ? JsonConvert.DeserializeObject<List<WorkflowEdgeDto>>(workflowData.Edges.ToString()) : new List<WorkflowEdgeDto>()
            };
        }

        public async Task<bool> UpdateMasterWorkflowAsync(Guid id, CreateMasterWorkflowDto masterWorkflow)
        {
            var existingWorkflow = await _dbContext.MasterWorkflows
                .FirstOrDefaultAsync(w => w.Id == id && !w.IsDeleted);

            if (existingWorkflow == null)
            {
                throw new Exception("Master workflow not found");
            }

            // Check if another workflow with the same name exists (excluding current one)
            var duplicateWorkflow = await _dbContext.MasterWorkflows
                .FirstOrDefaultAsync(w => w.Name == masterWorkflow.Name && w.Id != id && !w.IsDeleted);

            if (duplicateWorkflow != null)
            {
                throw new Exception("Master workflow with this name already exists");
            }

            var workflowData = new
            {
                Nodes = masterWorkflow.Nodes,
                Edges = masterWorkflow.Edges
            };

            existingWorkflow.Name = masterWorkflow.Name;
            existingWorkflow.Description = masterWorkflow.Description;
            existingWorkflow.WorkflowJson = JsonConvert.SerializeObject(workflowData);
            existingWorkflow.UpdatedBy = _userIdentityService.UserId;
            existingWorkflow.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteMasterWorkflowAsync(Guid id)
        {
            var masterWorkflow = await _dbContext.MasterWorkflows
                .FirstOrDefaultAsync(w => w.Id == id && !w.IsDeleted);

            if (masterWorkflow == null)
            {
                throw new Exception("Master workflow not found");
            }

            masterWorkflow.IsDeleted = true;
            masterWorkflow.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();
            return true;
        }
    }
}