﻿using ClosedXML.Excel;
using Engageto.Attributes;
using EngagetoContracts.ContactContracts;
using EngagetoContracts.Services;
using EngagetoEntities.Dtos.ApiResponseDtos;
using EngagetoEntities.Dtos.CommonDtos;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;
using System.Runtime.InteropServices;
using System.Security.Claims;
using Engageto.Attributes;
using Azure;

namespace Engageto.Controllers.ContactControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ContactsController : ControllerBase
    {
        private readonly IContactRepositoryBase _contactRepository;
        private readonly IContactService _contactService;
        private readonly IUserIdentityService _userIdentityService;

        public ContactsController(
            IContactRepositoryBase contactRepository,
            IContactService contactService,
             IUserIdentityService userIdentityService)
        {
            _contactRepository = contactRepository;
            _contactService = contactService;
            _userIdentityService = userIdentityService;
        }
        [Route("/api/ContactsDetails")]
        [HttpPost]
        [Authorize]
        public ActionResult ContactsDetails([FromBody, Optional] ContactOperationsDto? Operations, [FromQuery, Required] Guid BusinessId, [FromQuery, Required] Guid UserId, [Range(1, 100), Required, FromQuery] int page = 1, [Range(1, 1000), Required, FromQuery] int per_page = 10)
        {
            try
            {
                var result = _contactRepository.GetContactDetailsAsync(Operations, BusinessId, UserId, page, per_page);
                return new JsonResult(result);
            }
            catch (Exception ex)
            {
                return BadRequest($"Error occurred: {ex.Message}");
            }
        }

        [Route("/api/ContactsDetailsByTag")]
        [HttpPost]
        [Authorize]
        public ActionResult ContactsDetailsByTag([FromQuery, Required] Guid BusinessId, [FromQuery, Required] Guid UserId, [FromBody, Required] TagsIds list)
        {

            try
            {
                var output = _contactRepository.ContactsDetailsByTag(list.TagIds, BusinessId, UserId);
                return new JsonResult(output);
            }
            catch (Exception ex)
            {
                return BadRequest($" {ex.Message}.");
            }
        }

        [Route("/api/GetOrCreateContacts")]
        [HttpPost]
        [Authorize]
        public async Task<IActionResult> GetOrCreateContact([FromQuery, Required] Guid businessId, [FromBody] EngagetoEntities.Dtos.ContactDtos.ContactDetailsDto contactInfo)
        {
            try
            {
                if (_userIdentityService.BusinessId.ToLower() != businessId.ToString().ToLower()) throw new UnauthorizedAccessException("Invalid Business");

                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                    throw new UnauthorizedAccessException("Invalid current user.");
                var result = await _contactRepository.GetContactByContactNumberAndBusinessId(businessId, contactInfo, currentUserId);
                return Ok(new ApiResponse<Contacts>
                {
                    Success = true,
                    Message = result.Item2,
                    Data = result.Item1
                });
            }
            catch (Exception ex)
            {
                return BadRequest($" {ex.Message}.");
            }
        }

        [HttpPost("excel")]
        [Authorize]
        public async Task<IActionResult> UploadExcelFile([FromQuery, Required] Guid businessId, [FromForm, Required] UploadFileDto uploadFileDto)
        {
            try
            {
                if (_userIdentityService.BusinessId.ToLower() != businessId.ToString().ToLower()) throw new UnauthorizedAccessException("Invalid Business");
                var result = await _contactService.UploadExcelFileAsync(uploadFileDto);

                return Ok(new ApiResponse<FileColumnDto>
                {
                    Data = result,
                    Success = true,
                    Message = "File uploaded successfully."
                });

            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse<string>
                {
                    Success = false,
                    Message = "An unexpected error occurred.",
                    Data = ex.Message
                });
            }
        }

        [HttpPost("batch")]
        [Authorize]
        public async Task<IActionResult> CreateBulkAsync([FromQuery, Required] Guid businessId, BulkContactUploadDto bulkContactUploadDto)
        {
            try
            {
                if (_userIdentityService.BusinessId.ToLower() != businessId.ToString().ToLower()) throw new UnauthorizedAccessException("Invalid Business");
                var result = await _contactService.CreateBulkUploadAsync(bulkContactUploadDto);

                return Ok(new ApiResponse<ContactImportTracker>
                {
                    Data = result,
                    Success = true,
                    Message = "File uploaded successfully."
                });

            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse<string>
                {
                    Success = false,
                    Message = "An unexpected error occurred.",
                    Data = ex.Message
                });
            }
        }

        [Route("/api/contactsImportTracker/{uploadedId}")]
        [HttpGet]
        [Authorize]
        public async Task<IActionResult> ContactImportTracker([FromQuery, Required] Guid businessId, int uploadedId)
        {
            try
            {
                if (_userIdentityService.BusinessId.ToLower() != businessId.ToString().ToLower()) throw new UnauthorizedAccessException("Invalid Business");

                var contactImportTracker = await _contactService.GetContactTrackerByIdAsync(uploadedId);
                var result = new ApiResponse<ContactImportTracker>
                {
                    Data = contactImportTracker,
                    Success = true,
                    Message = "Successfully  contact Import trackerfetch..."
                };
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Message = ex.Message,
                    Error = ex.ToString(),
                    Success = false
                });
            }
        }

        [Route("/api/GetAllContactsImportTracker")]
        [HttpGet]
        [Authorize]
        public async Task<IActionResult> ContactImportTracker([FromQuery, Required] Guid businessId, int pageNumber, int pageSize)
        {
            try
            {
                if (_userIdentityService.BusinessId.ToLower() != businessId.ToString().ToLower()) throw new UnauthorizedAccessException("Invalid Business");
                var contactImportTracker = await _contactService.GetContactImportTrackersAsync(pageNumber, pageSize);
                var result = new ApiResponse<PaginatedImportTrackerDto>
                {
                    Data = contactImportTracker,
                    Success = true,
                    Message = "Successfully  contact Import trackerfetch..."
                };
                return Ok(result);

            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Message = ex.Message,
                    Error = ex.ToString(),
                    Success = false
                });
            }
        }

        [Route("/api/ContactsExport")]
        [HttpGet]
        //[AuthorizeMenu("export")]
        [Authorize]
        public async Task<IActionResult> ContactsExport([FromQuery, Required] Guid BusinessId, [FromQuery, Required] Guid UserId)
        {
            try
            {
                var stream = await _contactRepository.ContactExportAsync(BusinessId, UserId);
                string fileName = $"Contacts_{DateTime.UtcNow:yyyyMMddHHmmss}.xlsx";

                return File(
                    stream,
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    fileName
                );
            }
            catch (Exception ex)
            {
                return BadRequest($"Error exporting Contacts: {ex.Message}");
            }
        }


        [Route("/api/ContactsExportbyId")]
        [HttpPost]
        // [AuthorizeMenu("exportById")]
        [Authorize]
        public ActionResult ContactsExportbyId([FromBody, Required] ContactsIds list, [FromQuery, Required] Guid BusinessId, [FromQuery, Required] Guid UserId)
        {
            try
            {
                var dataTable = _contactRepository.ContactExportByIdAsync(list, BusinessId, UserId);

                using var workbook = new XLWorkbook();
                var worksheet = workbook.Worksheets.Add(dataTable);

                worksheet.Columns().AdjustToContents();

                worksheet.Row(1).Style.Font.Bold = true;
                worksheet.Row(1).Style.Fill.BackgroundColor = XLColor.LightGray;

                using var memoryStream = new MemoryStream();
                workbook.SaveAs(memoryStream);
                memoryStream.Position = 0;

                return File(
                    memoryStream.ToArray(),
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    $"Contacts_{DateTime.UtcNow:yyyyMMddHHmmss}.xlsx"
                );
            }
            catch (Exception ex)
            {
                return BadRequest($"Error exporting Contacts: {ex.Message}");
            }
        }


        [Route("/api/ContactAdd")]
        [HttpPost]
        //[AuthorizeMenu("addNewContact")]
        [Authorize]
        public async Task<IActionResult> ContactsAdd([FromQuery, Required] Guid BusinessId, [FromQuery, Required] Guid UserId, [FromBody] EngagetoEntities.Dtos.ContactDtos.ContactDetailsDto contactInfo)
        {
            try
            {
                var result = await _contactRepository.AddContactAsync(BusinessId, UserId, contactInfo);
                if (!result.IsArchive)
                {
                    return Ok(new ApiResponse<bool>
                    {
                        Success = true,
                        Message = result.message,
                        Data =  result.IsArchive
                    });
                }
                return Ok(new ApiResponse<bool>
                {
                    Success = true,
                    Message = result.message,
                    Data = result.IsArchive
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = "Error adding contact",
                    Error = ex.Message
                });
            }
        }

        [Route("/api/ContactDetails")]
        [HttpGet]
        [Authorize]
        public ActionResult ContactDetails([FromQuery, Required] Guid BusinessId, [FromQuery, Required] Guid UserId, [FromQuery, Required] Guid ContactId)
        {
            try
            {
                var result = _contactRepository.GetContactDetailsByContactIdAsync(BusinessId, UserId, ContactId);
                return new JsonResult(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }

        [Route("/api/contact/softdelete")]
        [HttpDelete]
        [Authorize]
        public async Task<ActionResult> ContactsSoftDeleteAsync([FromQuery, Required] Guid BusinessId, [FromBody, Required] ContactsIds list)
        {
            try
            {
                var softDeleteContact = await _contactRepository.DeActivateContactsAsync(list.ContactIds);
                return Ok(new ApiResponse<bool>
                {
                    Success = true,
                    Message = "Successfully contacts have been deactived.",
                    Data = softDeleteContact
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = "Error removing contacts",
                    Error = ex.Message
                });
            }
        }

        [Route("/api/contact/delete")]
        [HttpDelete]
        [Authorize]
        public async Task<ActionResult> ContactsDeleteAsync([FromQuery, Required] Guid BusinessId, [FromBody, Required] ContactsIds list)
        {
            try
            {
                bool deletedContacts = await _contactRepository.DeleteContactsAsync(list.ContactIds, BusinessId);
                return Ok(new ApiResponse<bool>
                {
                    Success = true,
                    Message = "Successfully contacts have been deleted.",
                    Data = deletedContacts
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = "Error removing contacts",
                    Error = ex.Message
                });
            }
        }

        [Route("/api/GetDeletedContact")]
        [HttpPost]
        [Authorize]
        public async Task<ActionResult> GetAllDeleted(
           [FromBody, Optional] ContactOperationsDto operations,
           [Range(1, 100), Required, FromQuery] int pageNumber = 1,
           [Range(1, 1000), Required, FromQuery] int pageSize = 10)
        {
            try
            {
                var result = await _contactRepository.GetAllDeletedContactsAsync(operations, pageNumber, pageSize);
                return Ok(new ApiResponse<PaginatedResult<ContactResponseDto>>
                {
                    Success = true,
                    Data = result,
                    Message = "Deleted contacts retrieved successfully.",
                    Error = null
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse<string>
                {
                    Success = false,
                    Message = "An unexpected error occurred.",
                    Data = null,
                    Error = ex.Message
                });
            }
        }

        [HttpPost("/api/RestoreContacts")]
        [Authorize]
        public async Task<ActionResult> RestoreContact([FromQuery, Required] Guid businessId, [FromQuery, Required] Guid userId, [FromBody, Required] ContactsIds list)
        {
            try
            {
                bool isRestored = await _contactRepository.RestoreDeletedContactAsync(businessId, userId, list.ContactIds);
                if (isRestored)
                {
                    return Ok(new { Message = "Contacts have been successfully restored." });
                }
                else
                {
                    return NotFound(new { Message = "Deleted/Deactivated contacts are not found with the provided criteria." });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"An unexpected error occurred: {ex.Message}" });
            }
        }

        [Route("/api/ContactTagsUpdate")]
        [HttpPut]
        //[AuthorizeMenu("editContacts")]
        [Authorize]

        public ActionResult ContactTagsUpdate([FromQuery, Required] Guid BusinessId, [FromQuery, Required] Guid UserId, [FromBody, Required] ContactsTagsUpdate data)
        {
            try
            {
                _contactRepository.ContactTagsUpdate(data.TagIds, data.ContactIds);
                return Ok(new
                {
                    Message = "Tags successfully updated for contacts."
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }

        [Route("/api/ContactTagsCreate")]
        [HttpPost]
        // [AuthorizeMenu("createTags")]
        [Authorize]
        public ActionResult TagsCreate([FromQuery, Required] Guid BusinessId, [FromQuery, Required] Guid UserId, [FromBody, Required] TagsDto tag)
        {
            try
            {
                _contactRepository.TagsCreate(tag.Tag, BusinessId, UserId);
                return Ok(new { Message = $"The tag was created successfully." });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });

            }
        }
        [Route("/api/ContactTagsDetails")]
        [HttpGet]
        [Authorize]
        public async Task<ActionResult> TagsDetails([FromQuery, Required] Guid BusinessId, [FromQuery, Required] Guid UserId, [FromQuery] string? search, [FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
        {
            try
            {
                var tags = await _contactRepository.GetTagDetailsAsync(BusinessId, UserId, search ?? string.Empty, pageNumber, pageSize);
                return Ok(new ApiResponse<PaginatedResult<Tags>>
                {
                    Success = true,
                    Message = "Tags retrieved successfully.",
                    Data = tags
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = "Error retrieving tags.",
                    Error = ex.Message
                });
            }
        }
        [Route("/api/ContactTagsDelete")]
        [HttpDelete]
        // [AuthorizeMenu("deleteTags")]
        [Authorize]
        public ActionResult TagsDelete([FromQuery, Required] Guid BusinessId, [FromQuery, Required] Guid UserId,
            [FromQuery, Required] Guid TagId)
        {
            try
            {
                if (TagId != Guid.Empty)
                {
                    _contactRepository.TagRemove(TagId, UserId);
                }
                return Ok(new { Message = $"Successfully tag has been deleted." });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }

        [Route("/api/ContactTagEdit")]
        [HttpPut]
        // [AuthorizeMenu("editTag")]
        [Authorize]
        public ActionResult TagEdit([FromQuery, Required] Guid BusinessId, [FromQuery, Required] Guid UserId,
            [FromBody, Required] TagRequestDto Tag)
        {
            try
            {

                if (Tag.Id != null)
                {
                    _contactRepository.TagEdit(Tag, UserId);
                }
                return Ok(new { Message = $"Successfully tag has been edited." });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }
        [Route("/api/ContactNoteAdd")]
        [HttpPut]
        // [AuthorizeMenu("createNotes")]
        [Authorize]
        public ActionResult NoteAdd([FromQuery, Required] Guid BusinessId, [FromQuery, Required] Guid UserId, [FromBody, Required] NotesDto Note)
        {
            try
            {
                _contactRepository.NoteAddToContact(Note.Note, Note.ContactId);
                return Ok(new { Message = "Notes have been successfully added." });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }
        [Route("/api/ContactNoteRemove")]
        [HttpPut]
        //[AuthorizeMenu("deleteNotes")]
        [Authorize]
        public ActionResult NoteRemove([FromQuery, Required] Guid BusinessId, [FromQuery, Required] Guid UserId, [FromBody, Required] NotesDto Note)
        {
            try
            {
                _contactRepository.NoteRemoveFromContact(Note.Note, Note.ContactId);
                return Ok(new
                {
                    Message = "Notes have been successfully removed."
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }

        [Route("/api/ContactEdit")]
        [HttpPost]
        // [AuthorizeMenu("editContacts")]
        [Authorize]
        public async Task<ActionResult> ContactEdit([FromQuery, Required] Guid BusinessId, [FromQuery, Required] Guid UserId, [FromQuery, Required] Guid ContactId, EngagetoEntities.Dtos.ContactDtos.ContactDetailsDto Contact)
        {
            try
            {
                await _contactRepository.UpdateContactAsync(Contact, ContactId, BusinessId, UserId);
                return Ok(new { Message = "Successfully edited contact." });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }
        [Route("/api/ContactTagsRemove")]
        [HttpPut]
        // [AuthorizeMenu("editContacts")]
        [Authorize]
        public ActionResult ContactTagsRemove([FromQuery, Required] Guid BusinessId, [FromQuery, Required] Guid UserId, [FromBody, Required] ContactsTagsRemoveDto data)
        {
            try
            {
                _contactRepository.ContactTagsRemove(data.Tag, data.ContactIds);
                return Ok(new { Message = "Tag successfully removed from contact." });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }

        [Route("/api/GetContactsByIds")]
        [HttpPost]
        [Authorize]
        public async Task<IActionResult> GetContactsByIds([FromBody, Required] ContactsIdsDto list)
        {
            if (list.ContactIds == null || !list.ContactIds.Any())
            {
                return BadRequest(new { Message = "Contact IDs list cannot be empty" });
            }
            try
            {
                var result = await _contactRepository.GetContactByIds(list);
                return Ok(new
                {
                    Success = true,
                    Message = "Contacts retrieved successfully",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Success = false,
                    Message = "Error retrieving contacts",
                    Error = ex.Message
                });
            }
        }

        [Route("CreateContactFromLeadrat")]
        [HttpPost]
        [Header("Api-Key")]
        [AllowAnonymous]
        [ApiKeyAuthenticationAttribute]
        public async Task<IActionResult> CreateLeadratContact([FromBody] LeadratLeadDto leadDto)
        {
            try
            {
                string companyId = HttpContext.Items["CompanyId"]?.ToString() ?? string.Empty;
                if (string.IsNullOrEmpty(leadDto.Name) || string.IsNullOrEmpty(leadDto.PhoneNumber))
                {
                    return BadRequest(new
                    {
                        Success = false,
                        Message = "Name and PhoneNumber are required fields."
                    });
                }

                var result = await _contactRepository.CreateLeadratContact(leadDto, companyId);

                return Ok(new ApiResponse<object>
                {
                    Success = true,
                    Message = result.Message,
                    Data = result.Contact
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Success = false,
                    Message = "Failed to create/update contact",
                    Errors = ex.Message
                });
            }
        }

        [Route("/api/GetAllSubSourcesBySource")]
        [HttpGet]
        [Authorize]
        public async Task<IActionResult> GetAllSubSourcesBySource([FromQuery, Required] Guid BusinessId)
        {
            try
            {
                var result = await _contactRepository.GetAllSubSourcesBySourceAsync(BusinessId);

                return Ok(new ApiResponse<object>
                {
                    Success = true,
                    Message = "Source with associated subsources retrieved successfully.",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Success = false,
                    Message = "Failed to retrieve sources with subsources!",
                    Errors = ex.Message
                });
            }
        }

        [Route("BlockContact")]
        [HttpPost]
        [Authorize]
        public async Task<IActionResult> BlockContact([FromQuery, Required] Guid businessId, [FromQuery, Required] string contactNumber)
        {
            try
            {
                if (_userIdentityService.BusinessId.ToLower() != businessId.ToString().ToLower())
                    throw new UnauthorizedAccessException("Invalid Business");

                var isBlock = await _contactRepository.BlockContactsAsync(contactNumber);
                if (isBlock)
                {
                    var successResponse = new ApiResponse<bool>
                    {
                        Success = true,
                        Data = isBlock,
                        Message = "Contact has been successfully blocked via Meta API."
                    };
                    return Ok(successResponse);
                }
                else
                {
                    var notFoundResponse = new ApiResponse<bool>
                    {
                        Success = false,
                        Message = "Contact not found or failed to block via Meta API."
                    };
                    return NotFound(notFoundResponse);
                }
            }
            catch (Exception ex)
            {
                var errorResponse = new ApiResponse<bool>
                {
                    Success = false,
                    Message = $"An error occurred while blocking contact: {ex.Message}"
                };
                return BadRequest(errorResponse);
            }
        }

        [Route("UnblockContact")]
        [HttpPost]
        [Authorize]
        public async Task<IActionResult> UnblockContact([FromQuery, Required] Guid businessId, [FromQuery, Required] string contactNumber)
        {
            try
            {
                if (_userIdentityService.BusinessId.ToLower() != businessId.ToString().ToLower())
                    throw new UnauthorizedAccessException("Invalid Business");

                var isUnBlock = await _contactRepository.UnblockContactAsync(contactNumber);
                if (isUnBlock)
                {
                    var result = new ApiResponse<bool>
                    {
                        Success = true,
                        Data = isUnBlock,
                        Message = "Contact successfully unblocked."
                    };
                    return Ok(result);
                }
                else
                {
                    var notFoundResult = new ApiResponse<bool>
                    {
                        Success = false,
                        Message = "Contact not found or failed to unblock via Meta API."
                    };
                    return NotFound(notFoundResult);
                }
            }
            catch (Exception ex)
            {
                var errorResult = new ApiResponse<bool>
                {
                    Success = false,
                    Message = $"An error occurred while unblocking the contact: {ex.Message}"
                };
                return BadRequest(errorResult);
            }
        }

        [Route("GetAllBlockedContacts")]
        [HttpGet]
        [Authorize]
        public async Task<IActionResult> GetAllBlockedContacts([FromQuery, Required] Guid businessId)
        {
            try
            {
                if (_userIdentityService.BusinessId.ToLower() != businessId.ToString().ToLower())
                    throw new UnauthorizedAccessException("Invalid Business");

                var blockedContacts = await _contactRepository.GetAllBlockedContactsAsync();
                var result = new ApiResponse<List<object>>
                {
                    Data = blockedContacts,
                    Success = true,
                    Message = $"Successfully retrieved {blockedContacts.Count} blocked contacts."
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Message = ex.Message,
                    Error = ex.ToString(),
                    Success = false
                });
            }
        }
    }
}


