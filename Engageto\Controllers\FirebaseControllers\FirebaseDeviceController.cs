using EngagetoContracts.FirebaseContracts;
using EngagetoContracts.Services;
using EngagetoEntities.Dtos;
using FirebaseAdmin.Messaging;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Engageto.Controllers.FirebaseControllers
{
    [Route("api/firebase/devices")]
    [ApiController]
    [Authorize]
    public class FirebaseDeviceController : ControllerBase
    {
        private readonly IFirebasePushNotificationService _firebaseService;
        private readonly ILogger<FirebaseDeviceController> _logger;
        private readonly IFirebaseConfigurationService _firebaseConfig;
        private readonly IConfiguration _configuration;
        private readonly IUserIdentityService _userIdentityService;

        public FirebaseDeviceController(
            IFirebasePushNotificationService firebaseService,
            ILogger<FirebaseDeviceController> logger,
            IFirebaseConfigurationService firebaseConfig,
            IConfiguration configuration, 
            IUserIdentityService  userIdentityService
            )
        {
            _firebaseService = firebaseService;
            _logger = logger;
            _firebaseConfig = firebaseConfig;
            _configuration = configuration;
            _userIdentityService = userIdentityService;
        }
        [HttpPut("device-registration")]
        public async Task<IActionResult> RegisterDevice([FromBody] FirebaseDeviceRegistrationDto deviceDto)
        {
            try
            {
                var userId = _userIdentityService.UserId;
                var businessId =  Guid.Parse(_userIdentityService.BusinessId);

                if (userId == Guid.Empty || businessId == Guid.Empty)
                {
                    return BadRequest(new { message = "Invalid user or business context" });
                }

                var result = await _firebaseService.RegisterDeviceAsync(deviceDto, userId, businessId);

                _logger.LogInformation("Device registered successfully for user {UserId}, device {DeviceId}",
                    userId, deviceDto.DeviceId);

                return Ok(new
                {
                    success = true,
                    message = "Device registered successfully",
                    data = result
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to register device");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Failed to register device",
                    error = ex.Message
                });
            }
        }
        [HttpDelete("{deviceId}")]
        public async Task<IActionResult> RemoveDevice(string deviceId)
        {
            try
            {
                var userId = _userIdentityService.UserId;
                var businessId = Guid.Parse(_userIdentityService.BusinessId);

                if (userId == Guid.Empty || businessId == Guid.Empty)
                {
                    return BadRequest(new { message = "Invalid user or business context" });
                }

                var result = await _firebaseService.RemoveDeviceAsync(deviceId, userId, businessId);

                if (result)
                {
                    _logger.LogInformation("Device removed successfully for user {UserId}, device {DeviceId}",
                        userId, deviceId);

                    return Ok(new
                    {
                        success = true,
                        message = "Device removed successfully"
                    });
                }
                else
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "Device not found"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to remove device {DeviceId}", deviceId);
                return StatusCode(500, new
                {
                    success = false,
                    message = "Failed to remove device",
                    error = ex.Message
                });
            }
        }


        [HttpPost("{deviceId}/device-info")]
        public async Task<IActionResult> UpdateDeviceInfo(string deviceId, [FromBody] FirebaseDeviceInfoUpdateDto updateDto)
        {
            try
            {
                var userId = _userIdentityService.UserId;
                var businessId = Guid.Parse(_userIdentityService.BusinessId);

                if (userId == Guid.Empty || businessId == Guid.Empty)
                {
                    return BadRequest(new { message = "Invalid user or business context" });
                }

                var result = await _firebaseService.UpdateDeviceInfoAsync(deviceId, updateDto, userId, businessId);

                if (result != null)
                {
                    _logger.LogInformation("Device info updated successfully for user {UserId}, device {DeviceId}",
                        userId, deviceId);

                    return Ok(new
                    {
                        success = true,
                        message = "Device info updated successfully",
                        data = result
                    });
                }
                else
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "Device not found"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update device info for device {DeviceId}", deviceId);
                return StatusCode(500, new
                {
                    success = false,
                    message = "Failed to update device info",
                    error = ex.Message
                });
            }
        }
        [HttpGet("my-devices")]
        public async Task<IActionResult> GetMyDevices()
        {
            try
            {
                var userId = _userIdentityService.UserId;
                var businessId = Guid.Parse(_userIdentityService.BusinessId);

                if (userId == Guid.Empty || businessId == Guid.Empty)
                {
                    return BadRequest(new { message = "Invalid user or business context" });
                }

                var devices = await _firebaseService.GetUserDevicesAsync(userId, businessId);

                return Ok(new
                {
                    success = true,
                    message = "Devices retrieved successfully",
                    data = devices,
                    count = devices.Count
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get user devices");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Failed to get user devices",
                    error = ex.Message
                });
            }
        }
        [HttpPost("test-notification")]
        public async Task<IActionResult> SendTestNotification([FromBody] FirebasePushNotificationDto request)
        {
            try
            {
                _logger.LogInformation("Sending test notification to current user's devices...");

                var userId = _userIdentityService.UserId;
                var businessId = Guid.Parse(_userIdentityService.BusinessId);

                if (userId == Guid.Empty || businessId == Guid.Empty)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Invalid user or business context. Please ensure you're authenticated."
                    });
                }

                _logger.LogInformation("Fetching FCM tokens for user {UserId} in business {BusinessId}", userId, businessId);

                // Send notification to user's devices using the service method
                var result = await _firebaseService.SendNotificationToUserAsync(userId, businessId, request);

                return Ok(new
                {
                    success = result.Success,
                    message = result.Success ? "Test notification sent successfully" : "Failed to send test notification",
                    data = result,
                    userInfo = new
                    {
                        userId = userId,
                        businessId = businessId,
                        totalDevicesTargeted = result.TotalDevices
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send test notification");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Failed to send test notification",
                    error = ex.Message
                });
            }
        }



        [HttpPost("send")]
        public async Task<IActionResult> SendNotification([FromBody]  FirebasePushNotificationDto  firebasePushNotification ,  string fcmToken)
        {
            if (string.IsNullOrEmpty(fcmToken))
            {
                return BadRequest("FCM Token is required.");
            }

            var notification = new FirebasePushNotificationDto
            {
                Title = firebasePushNotification.Title,
                Body = firebasePushNotification.Body
            };

            var response = await _firebaseService.SendNotificationToTokensAsync(fcmToken, notification);

            if (response.Success)
                return Ok(response);
            else
                return StatusCode(500, response);
        }


    }
}
