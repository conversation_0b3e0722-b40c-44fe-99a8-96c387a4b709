using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using EngagetoContracts.Services;
using EngagetoEntities.Dtos.MasterWorkflowDtos;
using Engageto.Controllers;
using EngagetoEntities.Dtos.ApiResponseDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Dtos.WorkflowDtos;

namespace Engageto.Controllers.MasterWorkflowController
{
    [Route("api/[controller]")]
    [ApiController]
    public class MasterWorkflowController : BaseController
    {
        private readonly IMasterWorkflowService _masterWorkflowService;
        public MasterWorkflowController(IMasterWorkflowService masterWorkflowService)
        {
            _masterWorkflowService = masterWorkflowService;
        }

        [HttpPost]
        [Authorize]
        public async Task<IActionResult> CreateMasterWorkflow([FromBody] CreateMasterWorkflowDto masterWorkflowDto)
        {
            try
            {
               var result = await _masterWorkflowService.CreateMasterWorkflowAsync(masterWorkflowDto);
                return Ok(new ApiResponse<MasterWorkflowResponseDto>
                {
                    Success = true,
                    Message = "Master workflow created successfully",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = "Failed to create master workflow",
                    Error = ex.Message
                });
            }
        }

        [HttpGet]
        [Authorize]
        public async Task<IActionResult> GetAllMasterWorkflows()
        {
            try
            {
                var workflows = await _masterWorkflowService.GetAllMasterWorkflowsAsync();

                return Ok(new ApiResponse<List<MasterWorkflowResponseDto>> // adjust type as per your DTO/model
                {
                    Success = true,
                    Message = "Master workflows retrieved successfully",
                    Data = workflows
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = "Failed to retrieve master workflows",
                    Error = ex.Message
                });
            }
        }

        [HttpGet("{id}")]
        [Authorize]
        public async Task<IActionResult> GetMasterWorkflowById(Guid id)
        {
            try
            {
                var workflow = await _masterWorkflowService.GetMasterWorkflowByIdAsync(id);
                return Ok(new ApiResponse<MasterWorkflowResponseDto>
                {
                    Success = true,
                    Message = "Master workflows retrieved successfully",
                    Data = workflow
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = "Failed to retrieve master workflows",
                    Error = ex.Message
                });
            }
        }

        [HttpPut("{id}")]
        [Authorize]
        public async Task<IActionResult> UpdateMasterWorkflow(Guid id, [FromBody] CreateMasterWorkflowDto masterWorkflowDto)
        {
            try
            {
                var result = await _masterWorkflowService.UpdateMasterWorkflowAsync(id, masterWorkflowDto);
                return Ok(new ApiResponse<bool>
                {
                    Success =  true,
                    Message = "Master workflow updated successfully",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = "Failed to update master workflow",
                    Error = ex.Message
                });
            }
        }

        [HttpDelete("{id}")]
        [Authorize]
        public async Task<IActionResult> DeleteMasterWorkflow(Guid id)
        {
            try
            {
                var result = await _masterWorkflowService.DeleteMasterWorkflowAsync(id);
                return Ok(new ApiResponse<bool>
                {
                    Success = true,
                    Message = "Master workflow deleted successfully",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = "Failed to update master workflow",
                    Error = ex.Message
                });
            }
        }

        [HttpPost("CreateFromMaster")]
        [Authorize]
        public async Task<IActionResult> CreateWorkflowFromMaster([FromBody] CreateWorkflowFromMasterDto request)
        {
            try
            {
                var result = await _masterWorkflowService.CreateWorkflowFromMasterAsync(request);
                return Ok(new ApiResponse<ViewWorkFlowDto>
                {
                    Success = true,
                    Message = "Workflow created from master successfully",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = "Failed to create workflow from master",
                    Error = ex.Message
                });
            }
        }
    }
}