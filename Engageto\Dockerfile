#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:7.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:7.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["Engageto/Engageto.csproj", "Engageto/"]
COPY ["EngagetoBackGroundJobs/EngagetoBackGroundJobs.csproj", "EngagetoBackGroundJobs/"]
COPY ["EngagetoContracts/EngagetoContracts.csproj", "EngagetoContracts/"]
COPY ["EngagetoEntities/EngagetoEntities.csproj", "EngagetoEntities/"]
COPY ["EngagetoDapper/EngagetoDapper.csproj", "EngagetoDapper/"]
COPY ["EngagetoRepository/EngagetoRepository.csproj", "EngagetoRepository/"]
RUN dotnet restore "./Engageto/Engageto.csproj"
COPY . .
WORKDIR "/src/Engageto"
RUN dotnet build "./Engageto.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./Engageto.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Engageto.dll"]