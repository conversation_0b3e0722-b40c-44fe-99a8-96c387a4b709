{
  "ConnectionStrings": {

    "ConnStr": "Data Source=engageto.database.windows.net;Initial Catalog=qa-engageto;User Id=dbmasteruser;Password=************`H9hTJK5ojM)C=$C69,2;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Max Pool Size=100;Min Pool Size=5;"
    // production database

    //"ConnStr": "Data Source=engageto-prd.database.windows.net;Initial Catalog=engageto-prd;User Id=dbmasteruser;Password=************`H9hTJK5ojM)C=$C69,1;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30; Max Pool Size=500;Min Pool Size=5;"

  },
  "CacheSettings": {
    "CacheExpirationTime": 30
  },
  "Origins": {
    "ProdWebsiteMain": "https://app.engageto.in",
    "ProdWebsiteAlternate": "https://app.engageto.in",
    "DevWebsiteMain": "https://app.engagetod.in",
    "DevWebsiteAlternate": "https://app.engagetod.in",
    "LocalWebsite": "http://localhost:3000",
    "StaticWebsiteMain": "https://www.engageto.com",
    "StaticWebsiteAlternate": "https://engageto.com",
    "LocalWebUrl": "http://*************:3000"
  },
  "AWS": {
    "BucketName": "dev-engageto",
    "SecretKey": "XcDOxHtp//5J0eHMc/TL1vKL61P4imzQ4cJs/cpi",
    "AccessKey": "********************",
    "Region": "ap-south-1"
  },
  "MetaApi": {
    "Version": "v23.0"
  },

  "MetaMediaFileSize": {
    "Image": 5242880, // 5MB in bytes
    "Document": 104857600, // 100MB in bytes
    "Video": 16777216, // 16MB in bytes
    "Audio": 16777216 // 16MB in bytes
  },

  "SmtpSettings": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "SmtpUsername": "*******",
    "SmtpPassword": "ydtp tfzl zeew disw",
    "LogoUrl": "https://dev-engageto.s3.ap-south-1.amazonaws.com/cropped-Engageto-logo-01-2048x684 (1)_1735632797.png",
    "EmailWebsite": "https://engageto.com"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "JWT": {
    "ValidAudience": "https://loginAPI/api",
    "ValidIssuer": "https://dev-jvgzpy2caz4fvqso.us.auth0.com/",
    "Secret": "****************************************************************"
  },
  "Razorpay": {
    "ApiKey": "***********************",
    "ApiSecret": "Bu190oDFyKbeNOssavWBzmrB"
  },
  //"Razorpay": {
  // "ApiKey": "rzp_test_7yCWLsSBbCVeUe",
  //  "ApiSecret": "cxGeTBJ9f37cRrNcmD3YY28K"
  //},
  "Facebook": {
    "AppId": "****************",
    "AppSecret": "c15cbd10a4e09fa7a778e7811d8981c6",
    "AccessToken": "******************************************************************************************************************************************************************************************************",
    "AppName": "Engageto",
    "BusinessId": "****************",
    "WhatsAppBusinessAccountID": "***************",
    "PhoneNumberId": "***************",
    "BusinessIdLocal": "********-7B1D-4C2D-92A6-FBA9CA31A261"
  },
  "ExampleForImport": {
    "File": "https://dev-engageto.s3.ap-south-1.amazonaws.com/5c9d87ee-a3b7-4b6f-a591-0b32dfd10450_11/08/2024 05:29:41.xlsx"
  },
  "DailyCo": {
    "BaseUrl": "https://api.daily.co/v1/",
    "ApiKey": "607c01e050ea3eaeadad375ddc46d24490acd7309aa1a662c30c732e1154a6e1"
  },
  "DemoConductor": {
    "Email": "*******",
    "Name": "Sravan"
  },
  "GstDetails": {
    "IGSTPercentage": 18
  },
  "DemoContactSaving": {
    "Message": "Hey! I have a question "
  },
  "Webhooks": {
    "VerifyToken": "12345"
  },
  "PaymentGateway": {
    "CashfreeOption": {
      "ApiKey": "793752990408046f8ad32f2cc9257397",
      "Secret": "cfsk_ma_prod_35ca8999f39a33432bc9fb8c2847da46_16dd97d6",
      "ApiVersion": "2022-09-01"
    }
  },
  //"PaymentGateway": {
  //  "CashfreeOption": {
  //    "ApiKey": "TEST1034386757aa3dee1e96e9dd77c476834301",
  //    "Secret": "cfsk_ma_test_8b5aa7179cdd111e7c299337caf870be_082cd524",
  //    "ApiVersion": "2022-09-01"
  //  }
  //},
  "AdminEmails": {
    "Emails": [ "*******", "*******", "*******" ]
  },
  "TemplateFiles": {
    "Wallet": "https://dev-engageto.s3.ap-south-1.amazonaws.com/Wallet Transaction_1737617433.pdf",
    "Subscriptions": "https://dev-engageto.s3.ap-south-1.amazonaws.com/subscription_deductions_invoice_1737617601.pdf"
  },
  "FunctionSettings": {
    "Dev_ScheduleJobUrl": "https://qa-engageto-background-app.azurewebsites.net/api/JobScheduleRequest?code=3vayGW-FSSBAAlJdk2RFbEj5f7RkvTu9cvhm3te9k-M9AzFu7ZXofw==",
    "Dev_TerminateJobUrl": "https://qa-engageto-background-app.azurewebsites.net/api/TerminatJob?code=niR6S3IMDpBTZF2omvZVn5DYiSnB3Z_AEQhl07Ap6AWhAzFu75PbXQ==",
    "Dev_JobStatusUrl": "https://qa-engageto-background-app.azurewebsites.net/runtime/webhooks/durabletask/instances/{jobId}?code=zIgTs7CDAjFGVpsDQF3QJINeuy0rJK78mCAoowcxOG2zAzFu55g6_w==",

    "Prod_ScheduleJobUrl": "https://prd-engageto-background-app.azurewebsites.net/api/JobScheduleRequest?code=RpgT8-hgFA0kz4Id8cp_eSqx2MDCIFI3muwJ3DPa5GE7AzFu6avuwQ==",
    "Prod_TerminateJobUrl": "https://prd-engageto-background-app.azurewebsites.net/api/TerminatJob?code=5KcI0jnClaKoPhUOeQ2dsHePInJoOQraD5Z-dEWWRcsYAzFujaf7Cg==",
    "Prod_JobStatusUrl": "https://prd-engageto-background-app.azurewebsites.net/runtime/webhooks/durabletask/instances/{jobId}?code=Zy1NrvdEg4-P6_9to0-mUo9QI7tJBfMVASHRfHEIsggXAzFuyQ3fMw=="
  },
  "FirebaseSetting":
    {
      "type": "service_account",
      "project_id": "engageto-prd",
      "private_key_id": "feba64b1597946c01c3297e56cb9e608e9d16235",
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      "client_email": "*******",
      "client_id": "107866264218529866633",
      "auth_uri": "https://accounts.google.com/o/oauth2/auth",
      "token_uri": "https://oauth2.googleapis.com/token",
      "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
      "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40engageto-prd.iam.gserviceaccount.com",
      "universe_domain": "googleapis.com"
    }
}
