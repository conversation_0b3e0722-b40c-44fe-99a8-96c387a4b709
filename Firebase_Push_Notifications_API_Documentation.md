# Firebase Web Push Notifications API Documentation

## Overview
This documentation provides comprehensive information about the Firebase Web Push Notification system integrated with WhatsApp webhook messages in the Engageto backend application.

## Features Implemented
✅ **Device Registration Management**
- Register browser/device for push notifications
- Update device information
- Remove device registration
- List user devices

✅ **Push Notification Services**
- Send notifications to specific users
- Send notifications to specific devices
- Send notifications to all business users
- Automatic WhatsApp message notifications

✅ **WhatsApp Integration**
- Automatic push notifications when WhatsApp messages are received
- Status update notifications (delivered, read, failed)
- Rich message preview with different media types

## Prerequisites

### 1. Database Migration
Run the database migration to create the Firebase device registration table:
```sql
-- The migration file is located at: Engageto/Migrations/20250123000001_AddFirebaseDeviceRegistration.cs
-- Run: dotnet ef database update --project Engageto
```

### 2. Firebase Configuration
The Firebase configuration is already set up in `appsettings.json`:
```json
{
  "Firebase": {
    "ProjectId": "engageto-ceaff",
    "CredentialsJson": "[Base64 encoded service account JSON]"
  }
}
```

### 3. Authentication
All Firebase APIs require JWT authentication. You need to:
1. <PERSON><PERSON> to get a JWT token
2. Include the token in the Authorization header: `Bearer {token}`

## API Endpoints

### Base URL
```
https://your-api-domain.com/api/firebase/devices
```

### 1. Register Device for Push Notifications

**Endpoint:** `PUT /api/firebase/devices/device-registration`

**Headers:**
```
Authorization: Bearer {jwt_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "deviceId": "unique-browser-device-id",
  "fcmToken": "firebase-cloud-messaging-token",
  "deviceType": "Web",
  "browserName": "Chrome",
  "browserVersion": "120.0.0.0",
  "operatingSystem": "Windows 10",
  "timeZone": "Asia/Kolkata",
  "language": "en"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Device registered successfully",
  "data": {
    "id": "guid",
    "deviceId": "unique-browser-device-id",
    "deviceType": "Web",
    "browserName": "Chrome",
    "isActive": true,
    "createdAt": "2025-01-23T10:30:00Z",
    "updatedAt": "2025-01-23T10:30:00Z",
    "lastUsedAt": "2025-01-23T10:30:00Z"
  }
}
```

### 2. Remove Device Registration

**Endpoint:** `DELETE /api/firebase/devices/{deviceId}`

**Headers:**
```
Authorization: Bearer {jwt_token}
```

**Response:**
```json
{
  "success": true,
  "message": "Device removed successfully"
}
```

### 3. Update Device Information

**Endpoint:** `POST /api/firebase/devices/{deviceId}/device-info`

**Headers:**
```
Authorization: Bearer {jwt_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "browserName": "Firefox",
  "browserVersion": "121.0.0",
  "operatingSystem": "macOS",
  "timeZone": "America/New_York",
  "language": "es",
  "isActive": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Device info updated successfully",
  "data": {
    "id": "guid",
    "deviceId": "unique-browser-device-id",
    "deviceType": "Web",
    "browserName": "Firefox",
    "isActive": true,
    "createdAt": "2025-01-23T10:30:00Z",
    "updatedAt": "2025-01-23T10:35:00Z",
    "lastUsedAt": "2025-01-23T10:35:00Z"
  }
}
```

### 4. Get User Devices

**Endpoint:** `GET /api/firebase/devices/my-devices`

**Headers:**
```
Authorization: Bearer {jwt_token}
```

**Response:**
```json
{
  "success": true,
  "message": "Devices retrieved successfully",
  "data": [
    {
      "id": "guid",
      "deviceId": "unique-browser-device-id",
      "deviceType": "Web",
      "browserName": "Chrome",
      "isActive": true,
      "createdAt": "2025-01-23T10:30:00Z",
      "updatedAt": "2025-01-23T10:30:00Z",
      "lastUsedAt": "2025-01-23T10:30:00Z"
    }
  ],
  "count": 1
}
```

### 5. Send Test Notification

**Endpoint:** `POST /api/firebase/devices/test-notification`

**Headers:**
```
Authorization: Bearer {jwt_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "title": "Test Notification",
  "body": "This is a test push notification",
  "icon": "/icons/notification-icon.png",
  "image": "/images/notification-banner.jpg",
  "clickAction": "/dashboard",
  "data": {
    "type": "test",
    "timestamp": "1706000000"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Test notification sent successfully",
  "data": {
    "success": true,
    "totalDevices": 1,
    "successfulDeliveries": 1,
    "failedDeliveries": 0
  }
}
```

## Testing with Postman/cURL

### 1. Get Authentication Token
First, login to get a JWT token:

```bash
curl -X POST "https://your-api-domain.com/api/authentication/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your-password"
  }'
```

### 2. Register Device
```bash
curl -X PUT "https://your-api-domain.com/api/firebase/devices/device-registration" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "deviceId": "browser-123456",
    "fcmToken": "your-fcm-token-here",
    "deviceType": "Web",
    "browserName": "Chrome",
    "browserVersion": "120.0.0.0",
    "operatingSystem": "Windows 10",
    "timeZone": "Asia/Kolkata",
    "language": "en"
  }'
```

### 3. Send Test Notification
```bash
curl -X POST "https://your-api-domain.com/api/firebase/devices/test-notification" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Hello from Postman!",
    "body": "This is a test notification sent via API",
    "icon": "/icons/test-icon.png",
    "clickAction": "/dashboard"
  }'
```

### 4. Get User Devices
```bash
curl -X GET "https://your-api-domain.com/api/firebase/devices/my-devices" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 5. Remove Device
```bash
curl -X DELETE "https://your-api-domain.com/api/firebase/devices/browser-123456" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## WhatsApp Integration

### Automatic Notifications
When a WhatsApp message is received via webhook, the system automatically:

1. **Parses the webhook payload** to extract:
   - Sender information (phone number, name)
   - Message content and type
   - Business context

2. **Creates a push notification** with:
   - Title: "New WhatsApp Message from {sender_name}"
   - Body: Preview of the message content
   - Click action: Direct link to conversation
   - Rich data payload with message details

3. **Sends to all business users** who have registered devices

### Webhook Endpoints
The following webhook endpoints now trigger push notifications:
- `POST /api/WAWebhookMessage/receive-WAmessage`
- `POST /api/WAWebhookMessage/receive-message`
- `POST /api/WAWebhookMessage/sent-message`

## Error Handling

### Common Error Responses

**401 Unauthorized:**
```json
{
  "success": false,
  "message": "Invalid user or business context"
}
```

**404 Not Found:**
```json
{
  "success": false,
  "message": "Device not found"
}
```

**500 Internal Server Error:**
```json
{
  "success": false,
  "message": "Failed to register device",
  "error": "Detailed error message"
}
```

## Frontend Integration

### Getting FCM Token (JavaScript)
```javascript
import { getMessaging, getToken } from "firebase/messaging";

const messaging = getMessaging();
const vapidKey = "your-vapid-key";

async function getFCMToken() {
  try {
    const token = await getToken(messaging, { vapidKey });
    console.log('FCM Token:', token);
    return token;
  } catch (error) {
    console.error('Error getting FCM token:', error);
  }
}
```

### Registering Device
```javascript
async function registerDevice() {
  const fcmToken = await getFCMToken();
  const deviceId = generateUniqueDeviceId(); // Your implementation
  
  const response = await fetch('/api/firebase/devices/device-registration', {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${jwtToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      deviceId,
      fcmToken,
      deviceType: 'Web',
      browserName: navigator.userAgent,
      // ... other device info
    })
  });
  
  const result = await response.json();
  console.log('Device registered:', result);
}
```

## Troubleshooting

### 1. Firebase Initialization Issues
- Check if Firebase credentials are correctly configured
- Verify the project ID matches your Firebase project
- Ensure the service account has proper permissions

### 2. Token Validation Errors
- Verify JWT token is valid and not expired
- Check if user has proper business context
- Ensure claims are correctly set in the token

### 3. Push Notification Delivery Issues
- Verify FCM token is valid and active
- Check if the device is marked as active
- Ensure Firebase project has Cloud Messaging enabled

### 4. WhatsApp Integration Issues
- Verify webhook endpoints are receiving data
- Check if business metadata is properly configured
- Ensure phone number ID mapping is correct

## Security Considerations

1. **FCM Tokens** are sensitive and should be treated securely
2. **Device IDs** should be unique and not easily guessable
3. **JWT Tokens** must be validated on every request
4. **Business Context** is enforced to prevent cross-tenant data access
5. **Rate Limiting** should be implemented for production use

## Production Deployment

1. **Environment Variables**: Store Firebase credentials as environment variables
2. **HTTPS**: Ensure all endpoints use HTTPS in production
3. **Rate Limiting**: Implement rate limiting for API endpoints
4. **Monitoring**: Set up logging and monitoring for push notification delivery
5. **Cleanup**: Implement periodic cleanup of inactive devices

## Support

For issues or questions:
1. Check the application logs for detailed error messages
2. Verify Firebase console for delivery statistics
3. Test with a simple notification first
4. Ensure all prerequisites are met
