# InboxSettingMessageAsync Plain Text Message Solution

## Overview
Modified the `InboxSettingMessageAsync` method to extract template body text and send it as plain text messages through the Cloud API instead of using WhatsApp templates. This includes support for both immediate and scheduled message delivery.

## Changes Made

### 1. **Added New Dependencies**
- Added `ISentMessageService` dependency to `WAWebhookHelper` class
- Added `using EngagetoContracts.WebhookContracts.SentMessage;` import

### 2. **Created ProcessTestMessage Method**
```csharp
public async Task ProcessTestMessage(string businessId, Guid contactId, string bodyMessage)
```
**Purpose**: Sends plain text messages through Cloud API instead of templates
**Features**:
- Retrieves contact details by ContactId and BusinessId
- Constructs phone number from contact's country code and contact number
- Creates `TextMessageRequest` with plain text body
- Sends message using `SentMessageService.SendMessageAsync()`

### 3. **Created ExtractTemplateBodyText Method**
```csharp
private string ExtractTemplateBodyText(Template template, List<string> variables)
```
**Purpose**: Extracts and processes template body text with variable substitution
**Features**:
- Takes template body text and variable values
- Replaces template variables `{{1}}`, `{{2}}`, etc. with actual values
- Returns processed plain text message

### 4. **Created ScheduleTestMessageJobAsync Method**
```csharp
private async Task<string> ScheduleTestMessageJobAsync(string methodName, object parameters, DateTimeOffset scheduledTime)
```
**Purpose**: Schedules plain text message jobs using Azure Functions
**Features**:
- Creates job request with method name, parameters, and scheduled time
- Calls Azure Function scheduler URL
- Returns job ID for tracking

### 5. **Modified InboxSettingMessageAsync Method**

#### **Welcome Message (New Contacts)**
```csharp
// OLD: await ProcessTemplateAsync(template, variables, businessId, phoneNumber);
// NEW:
var bodyMessage = ExtractTemplateBodyText(template, variables);
await ProcessTestMessage(businessId, contact.ContactId, bodyMessage);
```

#### **Out of Office Message**
```csharp
// OLD: await ProcessTemplateAsync(template, variables, businessId, phoneNumber);
// NEW:
var bodyMessage = ExtractTemplateBodyText(template, variables);
await ProcessTestMessage(businessId, contact.ContactId, bodyMessage);
```

#### **Regular Welcome Message**
```csharp
// OLD: await ProcessTemplateAsync(template, variables, businessId, phoneNumber);
// NEW:
var bodyMessage = ExtractTemplateBodyText(template, variables);
await ProcessTestMessage(businessId, contact.ContactId, bodyMessage);
```

#### **Delayed Response Message (Scheduled)**
```csharp
// OLD: Schedule ProcessTemplateAsync job
// NEW: Schedule ProcessTestMessage job
var bodyMessage = ExtractTemplateBodyText(template, variables);
var parameters = new
{
    BusinessId = businessId,
    ContactId = contact.ContactId,
    BodyMessage = bodyMessage
};
var jobId = await ScheduleTestMessageJobAsync("ProcessTestMessage", parameters, dateTimeOffset);
```

### 6. **Updated Interface**
Added `ProcessTestMessage` method to `IWAWebhookHelper` interface:
```csharp
Task ProcessTestMessage(string businessId, Guid contactId, string bodyMessage);
```

## Key Benefits

### ✅ **Plain Text Messaging**
- Messages are sent as regular text messages through Cloud API
- No longer dependent on WhatsApp template approval process
- Faster message delivery without template restrictions

### ✅ **Variable Processing**
- Template variables `{{1}}`, `{{2}}`, etc. are still processed and replaced
- Maintains existing variable substitution functionality
- Uses `InboxSettingsVariable` fallback values

### ✅ **Scheduling Support**
- Delayed response messages are properly scheduled
- Uses Azure Functions for background job processing
- Maintains existing delay functionality from template settings

### ✅ **Backward Compatibility**
- Original `ProcessTemplateAsync` method remains unchanged
- Interface maintains all existing methods
- No breaking changes to existing functionality

## Message Flow

### **Immediate Messages**
1. Template fetched from database
2. Variables retrieved from `InboxSettingsVariable`
3. Template body text extracted and variables replaced
4. Plain text message sent via `ProcessTestMessage`
5. Message delivered through Cloud API

### **Scheduled Messages**
1. Template fetched with delay settings
2. Variables retrieved and body text extracted
3. Job scheduled with Azure Functions
4. At scheduled time: `ProcessTestMessage` executes
5. Plain text message sent through Cloud API

## Configuration Requirements

### **Azure Function URLs**
Ensure these configuration keys are set:
- `FunctionSettings:Dev_ScheduleJobUrl` (Development)
- `FunctionSettings:Prod_ScheduleJobUrl` (Production)

### **Dependency Injection**
Ensure `ISentMessageService` is registered in DI container.

## Testing Recommendations

1. **Test immediate messages** for new contacts (Welcome Message)
2. **Test out-of-office messages** during configured off-hours
3. **Test delayed messages** with various delay settings
4. **Verify variable substitution** works correctly
5. **Test scheduling functionality** with Azure Functions

## Status: ✅ IMPLEMENTED

The `InboxSettingMessageAsync` method now sends plain text messages instead of WhatsApp templates, with full support for variable processing and message scheduling.
